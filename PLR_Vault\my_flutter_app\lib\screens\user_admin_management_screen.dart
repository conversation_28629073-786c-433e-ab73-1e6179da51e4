import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../utils/logger.dart';

class UserAdminManagementScreen extends StatefulWidget {
  const UserAdminManagementScreen({super.key});

  @override
  State<UserAdminManagementScreen> createState() =>
      _UserAdminManagementScreenState();
}

class _UserAdminManagementScreenState extends State<UserAdminManagementScreen> {
  List<AppUser> _allUsers = [];
  List<AdminUser> _admins = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      await Future.wait([_loadAllUsers(), _loadAdmins()]);
    } catch (e) {
      Logger.error('Error loading data', e);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadAllUsers() async {
    try {
      // Get all users from Firebase Auth (we'll simulate this with registered users)
      // In a real app, you'd need to track users in your database when they register
      final usersRef = FirebaseDatabase.instance.ref('users');
      final snapshot = await usersRef.once();

      final List<AppUser> users = [];
      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (final entry in data.entries) {
          users.add(AppUser.fromMap(entry.key, entry.value));
        }
      }

      setState(() => _allUsers = users);
    } catch (e) {
      Logger.error('Error loading users', e);
    }
  }

  Future<void> _loadAdmins() async {
    try {
      final adminsRef = FirebaseDatabase.instance.ref('admins');
      final snapshot = await adminsRef.once();

      final List<AdminUser> admins = [];
      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (final entry in data.entries) {
          admins.add(AdminUser.fromMap(entry.value));
        }
      }

      setState(() => _admins = admins);
    } catch (e) {
      Logger.error('Error loading admins', e);
    }
  }

  bool _isUserAdmin(String email) {
    return _admins.any((admin) => admin.email == email);
  }

  Future<void> _makeAdmin(AppUser user) async {
    try {
      final emailKey = user.email.replaceAll('.', '_').replaceAll('@', '_');
      final adminData = {
        'email': user.email,
        'role': 'admin',
        'addedAt': DateTime.now().millisecondsSinceEpoch,
        'addedBy': FirebaseAuth.instance.currentUser?.email ?? 'system',
      };

      await FirebaseDatabase.instance.ref('admins/$emailKey').set(adminData);

      Logger.info('User ${user.email} made admin');
      await _loadAdmins(); // Refresh admin list

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${user.email} is now an admin'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      Logger.error('Error making user admin', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _removeAdmin(AdminUser admin) async {
    // Don't allow removing super admin
    if (admin.email == '<EMAIL>') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot remove super admin'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Admin'),
        content: Text('Remove ${admin.email} from admin role?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final emailKey = admin.email.replaceAll('.', '_').replaceAll('@', '_');
        await FirebaseDatabase.instance.ref('admins/$emailKey').remove();

        Logger.info('Admin ${admin.email} removed');
        await _loadAdmins(); // Refresh admin list

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${admin.email} removed from admin role'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        Logger.error('Error removing admin', e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Management'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current Admins Section
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Current Admins',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          if (_admins.isEmpty)
                            const Text('No admins found')
                          else
                            ..._admins.map(
                              (admin) => ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: admin.role == 'superAdmin'
                                      ? Colors.red
                                      : Colors.blue,
                                  child: Text(
                                    admin.email[0].toUpperCase(),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                title: Text(admin.email),
                                subtitle: Text(
                                  admin.role == 'superAdmin'
                                      ? 'Super Admin'
                                      : 'Admin',
                                ),
                                trailing:
                                    admin.email != '<EMAIL>'
                                    ? IconButton(
                                        icon: const Icon(
                                          Icons.remove_circle,
                                          color: Colors.red,
                                        ),
                                        onPressed: () => _removeAdmin(admin),
                                      )
                                    : const Icon(
                                        Icons.security,
                                        color: Colors.green,
                                      ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // All Users Section
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'All App Users',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              ElevatedButton.icon(
                                onPressed: _loadData,
                                icon: const Icon(Icons.refresh),
                                label: const Text('Refresh'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_allUsers.isEmpty)
                            const Text(
                              'No users found. Users appear here when they register.',
                            )
                          else
                            ..._allUsers.map((user) {
                              final isAdmin = _isUserAdmin(user.email);
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: isAdmin
                                      ? Colors.green
                                      : Colors.grey,
                                  child: Text(
                                    user.email[0].toUpperCase(),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                title: Text(user.email),
                                subtitle: Text(
                                  'Joined: ${_formatDate(user.joinedAt)}',
                                ),
                                trailing: isAdmin
                                    ? const Chip(
                                        label: Text('Admin'),
                                        backgroundColor: Colors.green,
                                      )
                                    : ElevatedButton(
                                        onPressed: () => _makeAdmin(user),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.blue,
                                        ),
                                        child: const Text('Make Admin'),
                                      ),
                              );
                            }),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class AppUser {
  final String email;
  final DateTime joinedAt;

  AppUser({required this.email, required this.joinedAt});

  factory AppUser.fromMap(String email, Map<dynamic, dynamic> map) {
    return AppUser(
      email: email,
      joinedAt: DateTime.fromMillisecondsSinceEpoch(map['joinedAt'] ?? 0),
    );
  }
}

class AdminUser {
  final String email;
  final String role;
  final DateTime addedAt;
  final String addedBy;

  AdminUser({
    required this.email,
    required this.role,
    required this.addedAt,
    required this.addedBy,
  });

  factory AdminUser.fromMap(Map<dynamic, dynamic> map) {
    return AdminUser(
      email: map['email'] ?? '',
      role: map['role'] ?? 'admin',
      addedAt: DateTime.fromMillisecondsSinceEpoch(map['addedAt'] ?? 0),
      addedBy: map['addedBy'] ?? '',
    );
  }
}
