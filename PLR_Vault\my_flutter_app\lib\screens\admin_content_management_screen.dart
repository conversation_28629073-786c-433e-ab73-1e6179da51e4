import 'package:flutter/material.dart';
import '../models/content_item.dart';
import '../services/realtime_database_service.dart';
import '../utils/logger.dart';
import 'add_content_screen.dart';

class AdminContentManagementScreen extends StatefulWidget {
  const AdminContentManagementScreen({super.key});

  @override
  State<AdminContentManagementScreen> createState() =>
      _AdminContentManagementScreenState();
}

class _AdminContentManagementScreenState
    extends State<AdminContentManagementScreen> {
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  List<String> _categories = [];
  Map<String, List<ContentItem>> _categoryContent = {};
  bool _isLoading = true;
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _loadContent();
  }

  Future<void> _loadContent() async {
    try {
      setState(() => _isLoading = true);

      final categories = await _dbService.getCategories();
      final Map<String, List<ContentItem>> categoryContent = {};

      for (final category in categories) {
        final content = await _dbService.getContentByCategory(category);
        if (content.isNotEmpty) {
          categoryContent[category] = content;
        }
      }

      setState(() {
        _categories = categoryContent.keys.toList();
        _categoryContent = categoryContent;
        _isLoading = false;
      });
    } catch (e) {
      Logger.error('Failed to load content', e);
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load content: $e')));
      }
    }
  }

  Future<void> _deleteContent(ContentItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Content'),
        content: Text('Are you sure you want to delete "${item.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbService.deleteContentItem(item.id);
        await _loadContent(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Content deleted successfully')),
          );
        }
      } catch (e) {
        Logger.error('Failed to delete content', e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to delete content: $e')),
          );
        }
      }
    }
  }

  Future<void> _editContent(ContentItem item) async {
    final titleController = TextEditingController(text: item.title);
    final descriptionController = TextEditingController(text: item.description);

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Content'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Title',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
                maxLines: 4,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context, {
                'title': titleController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null && result['title']!.isNotEmpty) {
      try {
        final updatedItem = ContentItem(
          id: item.id,
          title: result['title']!,
          description: result['description']!,
          category: item.category,
          fileUrl: item.fileUrl,
          fileName: item.fileName,
          fileType: item.fileType,
          fileSize: item.fileSize,
          createdAt: item.createdAt,
        );

        await _dbService.updateContentItem(updatedItem);
        await _loadContent(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Content updated successfully')),
          );
        }
      } catch (e) {
        Logger.error('Failed to update content', e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to update content: $e')),
          );
        }
      }
    }

    titleController.dispose();
    descriptionController.dispose();
  }

  Future<void> _addNewContent() async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddContentScreen(onContentAdded: _loadContent),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Content Management'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _addNewContent,
            icon: const Icon(Icons.add),
            tooltip: 'Add New Content',
          ),
          IconButton(
            onPressed: _loadContent,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _categories.isEmpty
          ? const Center(
              child: Text(
                'No content found',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            )
          : Column(
              children: [
                // Category selector
                Container(
                  padding: const EdgeInsets.all(16),
                  child: DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Select Category',
                      border: OutlineInputBorder(),
                    ),
                    items: [
                      const DropdownMenuItem<String>(
                        value: null,
                        child: Text('All Categories'),
                      ),
                      ..._categories.map(
                        (category) => DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() => _selectedCategory = value);
                    },
                  ),
                ),
                // Content list
                Expanded(child: _buildContentList()),
              ],
            ),
    );
  }

  Widget _buildContentList() {
    final categoriesToShow = _selectedCategory != null
        ? [_selectedCategory!]
        : _categories;

    return ListView.builder(
      itemCount: categoriesToShow.length,
      itemBuilder: (context, index) {
        final category = categoriesToShow[index];
        final items = _categoryContent[category] ?? [];

        return ExpansionTile(
          title: Text(
            category,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text('${items.length} items'),
          children: items.map((item) => _buildContentTile(item)).toList(),
        );
      },
    );
  }

  Widget _buildContentTile(ContentItem item) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(item.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(item.description),
            const SizedBox(height: 4),
            Text(
              '${item.fileType.toUpperCase()} • ${item.formattedFileSize}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editContent(item),
              icon: const Icon(Icons.edit, color: Colors.blue),
              tooltip: 'Edit',
            ),
            IconButton(
              onPressed: () => _deleteContent(item),
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete',
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }
}
