body{
	direction: ltr;
	background-color: #fff !important;
}

.gridContainer{
	width: 90%;
	max-width: 750px;
	margin: 0 auto;
	padding: 0 15px;
}
.mainBg{
	width: 85%;
	margin: 0 auto;
}
header{
	border-bottom: 34px solid #222325;
	background-color: #fff;
}
.attention{
	font-style: italic;
    text-align: center;
    color: #666;
    margin: 15px 0;
}
.attention p{
	font-size: 16px;
}
.attention p span{
	font-weight: bold;
}
.questionTitle{
	color: #000;
	text-align: center;
	margin: 15px 0; 
}
.questionTitle h1{
	font-weight: 700;
}
.recommended{
	color: #D50003;
	text-align: center;
	margin: 15px 0; 
}
.hugeFont{
	font-size: 13px;
}
.middleFont{
	font-size: 14px;
}
.subTitle{
	margin: 15px 0;
	text-align: center;
}
.subTitle p{
	color: #666666;
	font-size: 21px;
	font-weight: 700;
}
.subheadline {
	font-size: 22px;
	font-family: Arial, Helvetica, sans-serif;
	color: #666;
	text-align: center;
}
.mostPop{
	margin: 20px 0;
}
.mostPop p{
	font-size: 16px;
	color: #000;
	margin-bottom: 15px;
}
.mostPop p span{
	font-weight: bold;
}
.product-brief{
	text-align: center;
	color: #000;
}
.product-brief p{
	font-style: italic;
	font-size: 25px;
	font-weight: 500;
}
.productImage {
	width: 95%;
	margin: 15px auto;

}
.component{
	border:3px solid #000;
	width: 95%;
	max-width: 600px;
	margin: 30px auto;
}
.componentContainer{
	padding: 15px;
}
.componentImage{
	width: 95%;
	margin: 15px auto;
	max-width: 400px;
}
.component ul{
	margin: 20px 0;
	text-align: left;
	padding-left:40px; 
}
.component ul li{
	margin-bottom: 15px;
	text-align: left;
}
.component ul li span{
	font-size: 16px;
    color: #000;
  
}
.component ul li.bold span , .bold{
	font-weight: bold;
}
.beforecontinue{
	width: 100%;
	margin: 15px auto;
	max-width: 600px;
	height: auto !important;
}

.videos{
	width: 100%;
	margin: 5px auto;
	max-width: 600px;
	height: auto !important;
}
.mp4{
	width: 100%;
	margin: 5px auto;
	max-width: 256px;
	height: auto !important;
}

.Red{
	color: #C30003;
	font-size: 16px;
	margin: 50px 0 30px;
}
.howTo{

	margin: 50px 0; 
}
.risk{
	background-repeat: no-repeat;
	height: 365px;
	width: 90%;
	margin: 0 auto;
	max-width: 527px;
	display: block;
	position: relative;
}
.riskContainer{
	padding:99px 30px 0 35px;
	position: absolute;
	top: 0;
	right: 0;
}
.risk img{
	height: 100%;
}
.accessBox{
	border:6px dashed #9b0707;
	width: 95%;
	max-width: 600px;
	margin: 30px auto;
}
.accessBoxContainer{
	padding: 30px;
}

.accessBoxContainer ul{
	margin: 20px 0;
	text-align: left;
	padding-left:40px; 
}
.accessBoxContainer ul li{
	margin-bottom: 15px;
	text-align: left;
}
.accessBoxContainer ul li span{
	font-size: 18px;
    color: #000; 
}
.button{
text-align: center;
position: relative;
}
.button img{
	width: 90%;
max-width: 288px;

}
.button button{
	background:none;
	border:none;
	outline: none;
	width: 100%;
	height: 100%;
	top: 0;
	right: 0;
	z-index: 99;
	position: absolute;
}
footer{
	background-color: #fff;
}
.footerImage{
	width: 100%;
	max-width: 650px;
	margin: 0 auto
}


@media screen and (min-width: 650px){
	.hugeFont{
		font-size: 19px;
	}
}

