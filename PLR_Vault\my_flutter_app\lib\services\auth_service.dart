import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

export 'firebase_storage_service.dart';
export 'realtime_database_service.dart';
export 'download_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email']);

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      return await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Always sign out first to show account picker
      await _googleSignIn.signOut();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if we have the required tokens
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in with credential
      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );

      // Sign in successful - return the credential
      return userCredential;
    } catch (e) {
      print('Google Sign-In Error: $e'); // Debug log
      rethrow; // Re-throw errors for proper handling
    }
  }

  // Sign up with Google (creates account and signs out for login flow)
  Future<UserCredential?> signUpWithGoogle() async {
    try {
      // Always sign out first to show account picker
      await _googleSignIn.signOut();

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-up
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Check if we have the required tokens
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Create account by signing in
      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );

      // Account created successfully - now sign out the user so they need to sign in
      await _googleSignIn.signOut();
      await _auth.signOut();

      return userCredential;
    } catch (e) {
      print('Google Sign-Up Error: $e'); // Debug log
      rethrow; // Re-throw errors for proper handling
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
    } catch (e) {
      rethrow; // Re-throw to handle specific errors in UI
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Sign out from Google first
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }

      // Then sign out from Firebase
      await _auth.signOut();

      // Double check - force sign out if still signed in
      if (_auth.currentUser != null) {
        await _auth.signOut();
      }
    } catch (e) {
      // Force sign out even if there's an error
      try {
        await _auth.signOut();
      } catch (e2) {
        // Last resort - clear the auth state
      }
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        await user.delete();
        await _googleSignIn.signOut();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
