@charset "UTF-8";
/* CSS Document */

body {
	background-image:url(images/bg.jpg);
	background-repeat:repeat;
	margin:0px;
font-family:Arial, Helvetica, sans-serif;
}

.testimonialp {
	font-family:Georgia, "Times New Roman", Times, serif;
	font-style:italic;
	font-size:15px;
	margin-top:25px;
}
h1 {
	font-family:Tahoma, Verdana, Arial;
	font-size:27px;
	line-height:40px;
	text-align:center;
	letter-spacing:-1px;
}
h3 {
	font-family:Tahoma, Verdana, Arial;
	font-size:27px;
	line-height:40px;
	text-align:center;
	letter-spacing:-1px;
	margin-top:0px;
}
h4 {
	font-family:Georgia, "Times New Roman", Times, serif;
	font-size:21px;
	line-height:33px;
	letter-spacing:-1px;
	margin-top:20px;
	font-weight:bold;
	font-style:italic;
}
ul {
}
li {
	list-style:url(images/bullet.gif);
	font-family:Arial, Helvetica, sans-serif;
	font-size:15px;
	margin-bottom:25px;
	line-height:25px;
}
#maincontainer {
	background-image:url(images/bg-x.jpg);
	background-repeat:repeat-x;
	width:100%;
	height:auto;
}
#header {
	background-image: url(images/header.jpg);
	height: 206px;
	width: 710px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
#contentbg {
	background-image: url(http://contentwithplr.com/FirstListProfitsPLR/affys/images/contentbg.png);
	background-repeat: repeat-y;
	height: auto;
	width: 610px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	padding-right: 50px;
	padding-left: 50px;
	overflow: hidden;
}
.videoframe {
	background-image: url(images/videoframe.gif);
	height: 404px;
	width: 569px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 30px;
	margin-left: auto;
	padding-top: 9px;
	padding-left: 9px;
}
.johnsonbox-top {
	background-image:url(images/johnsonbox-top.gif);
	height: 27px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
.johnsonbox-mid {
	background-image: url(images/johnsonbox-mid.gif);
	background-repeat: repeat-y;
	height: auto;
	width: 519px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	padding-right: 30px;
	padding-left: 30px;
	overflow: hidden;
}
.johnsonbox-bottom {
	background-image: url(images/johnsonbox-bottom.gif);
	height: 27px;
	width: 579px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 30px;
	margin-left: auto;
	overflow: hidden;
}
.testimonialbox-top {
	background-image: url(images/testimonialbox-top.gif);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
.testimonialbox-top2 {
	background-image: url(images/testimonialbox-top2.gif);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
.testimonialbox-top3 {
	background-image: url(images/testimonialbox-top3.gif);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}

.testimonialbox-top4 {
	background-image: url(images/testimonialbox-top4.gif);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
.testimonialbox-top5 {
	background-image: url(images/testimonialbox-top5.gif);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}

.testimonialbox-top6 {
	background-image: url(images/testimonialbox-top6.png);
	height: 82px;
	width: 579px;
	margin-top: 30px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	overflow: hidden;
}
.testimonialbox-mid {
	background-image: url(images/testimonialbox-mid.gif);
	background-repeat: repeat-y;
	height: auto;
	width: 519px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
	padding-right: 30px;
	padding-left: 30px;
	overflow: hidden;
}
.testimonialbox-bottom {
	background-image: url(images/testimonialbox-bottom.gif);
	height: 27px;
	width: 579px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 30px;
	margin-left: auto;
	overflow: hidden;
}
#footer {
	background-image: url(images/footer.jpg);
	height: 133px;
	width: 710px;
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 50px;
	margin-left: auto;
	overflow: hidden;
}
div.box {
	width: 552px;
	padding: 10px 10px 10px 10px;
	background-image: url(images/boxbottom.jpg), url(images/boxtop.jpg), url(images/boxbg.jpg);
	background-repeat: no-repeat, no-repeat, repeat-y;
	background-position: bottom, top, top;
	margin: 30px auto;
	clear: both;
}