import 'package:flutter/foundation.dart';
import '../models/models.dart';
import 'admob_service.dart';
import 'realtime_database_service.dart';
import 'download_service.dart';

class ContentUnlockService {
  static final ContentUnlockService _instance =
      ContentUnlockService._internal();
  factory ContentUnlockService() => _instance;
  ContentUnlockService._internal();

  final AdMobService _adMobService = AdMobService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();
  final DownloadService _downloadService = DownloadService();

  /// Check if content requires unlocking (non-video content)
  bool requiresUnlocking(ContentItem content) {
    // Videos (mp4) are always unlocked
    return !content.isVideo;
  }

  /// Check if content is accessible (either unlocked or is a video)
  Future<bool> isContentAccessible(ContentItem content) async {
    // Videos are always accessible
    if (content.isVideo) {
      return true;
    }

    // Check if non-video content is unlocked
    return await _dbService.isContentUnlocked(content.id);
  }

  /// Get content access status
  Future<ContentAccessStatus> getContentAccessStatus(
    ContentItem content,
  ) async {
    if (content.isVideo) {
      return ContentAccessStatus.alwaysUnlocked;
    }

    final isUnlocked = await _dbService.isContentUnlocked(content.id);
    if (isUnlocked) {
      return ContentAccessStatus.unlocked;
    }

    return ContentAccessStatus.locked;
  }

  /// Attempt to unlock content by showing rewarded ad
  Future<UnlockResult> unlockContentWithAd(ContentItem content) async {
    try {
      // Videos don't need unlocking
      if (content.isVideo) {
        return UnlockResult.success('Video content is always available');
      }

      // Check if already unlocked
      final isAlreadyUnlocked = await _dbService.isContentUnlocked(content.id);
      if (isAlreadyUnlocked) {
        return UnlockResult.success('Content is already unlocked');
      }

      // Ensure ad is loaded
      if (!_adMobService.isRewardedAdReady) {
        await _adMobService.loadRewardedAd();

        // Wait a bit for ad to load
        await Future.delayed(const Duration(seconds: 2));

        if (!_adMobService.isRewardedAdReady) {
          return UnlockResult.failure(
            'Ad not available. Please try again later.',
          );
        }
      }

      // Show rewarded ad
      if (kDebugMode) {
        print('Showing rewarded ad for content: ${content.id}');
      }

      final rewardEarned = await _adMobService.showRewardedAd();

      if (kDebugMode) {
        print('Ad completed. Reward earned: $rewardEarned');
      }

      if (rewardEarned) {
        // Mark content as unlocked
        if (kDebugMode) {
          print('Marking content ${content.id} as unlocked in database');
        }

        await _dbService.unlockContent(content.id);

        if (kDebugMode) {
          print('Content ${content.id} unlocked successfully');
        }

        return UnlockResult.success(
          'Content unlocked! You can now download it.',
        );
      } else {
        if (kDebugMode) {
          print('Ad was not completed or reward not earned');
        }
        return UnlockResult.failure(
          'Ad was not completed. Content remains locked.',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unlocking content: $e');
      }
      return UnlockResult.failure('Failed to unlock content: ${e.toString()}');
    }
  }

  /// Download content after ensuring it's unlocked
  Future<DownloadResult> downloadUnlockedContent({
    required ContentItem content,
    required Function(double) onProgress,
  }) async {
    try {
      // Check if content is accessible
      final isAccessible = await isContentAccessible(content);
      if (!isAccessible) {
        return DownloadResult.failure(
          'Content is locked. Please unlock it first.',
        );
      }

      // Download the content
      final localPath = await _downloadService.downloadContent(
        content: content,
        onProgress: onProgress,
      );

      return DownloadResult.success(localPath);
    } catch (e) {
      return DownloadResult.failure('Download failed: ${e.toString()}');
    }
  }

  /// Preload ads for better user experience
  Future<void> preloadAds() async {
    await _adMobService.preloadAds();
  }

  /// Check if ads are ready
  bool get areAdsReady => _adMobService.isRewardedAdReady;

  /// Check if ads are loading
  bool get areAdsLoading => _adMobService.isLoading;
}

/// Enum for content access status
enum ContentAccessStatus {
  alwaysUnlocked, // Videos
  unlocked, // Non-video content that has been unlocked
  locked, // Non-video content that needs unlocking
}

/// Result class for unlock operations
class UnlockResult {
  final bool success;
  final String message;

  UnlockResult.success(this.message) : success = true;
  UnlockResult.failure(this.message) : success = false;
}

/// Result class for download operations
class DownloadResult {
  final bool success;
  final String? localPath;
  final String? errorMessage;

  DownloadResult.success(this.localPath) : success = true, errorMessage = null;
  DownloadResult.failure(this.errorMessage) : success = false, localPath = null;
}
