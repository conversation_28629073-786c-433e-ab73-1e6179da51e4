{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\PLR_Vault\\my_flutter_app\\build\\.cxx\\RelWithDebInfo\\4c1e3u5o\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Fiverr_Projects\\PLR_Vault\\my_flutter_app\\build\\.cxx\\RelWithDebInfo\\4c1e3u5o\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}