import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'screens/login_screen.dart';
import 'screens/categories_screen.dart';
import 'screens/admin_screen.dart';
import 'services/admob_service.dart';
import 'services/admin_auth_service.dart';
import 'utils/logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await AdMobService.initialize();
  await AdminAuthService.initializeSuperAdmin();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wealth Vault',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/categories': (context) => const CategoriesScreen(),
      },
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  /// Check admin status using UID (completely fresh database query)
  static Future<AdminUser?> _checkAdminStatus(String uid) async {
    try {
      Logger.info('🔍 STARTING FRESH ADMIN CHECK FOR UID: $uid');

      // Force complete session reset
      AdminAuthService.clearSession();

      // Add delay to ensure clearing is complete
      await Future.delayed(const Duration(milliseconds: 150));

      // Direct database query with fresh reference using UID
      Logger.info('🔍 Database path: admins/$uid');

      // Create completely new database reference
      final DatabaseReference freshRef = FirebaseDatabase.instance.ref(
        'admins/$uid',
      );
      final DatabaseEvent event = await freshRef.once();

      if (event.snapshot.exists && event.snapshot.value != null) {
        final data = event.snapshot.value as Map<dynamic, dynamic>;
        final adminUser = AdminUser.fromMap(data);
        Logger.info(
          '🎯 ADMIN FOUND IN DATABASE: ${adminUser.email} (UID: ${adminUser.uid}) - Role: ${adminUser.role.name}',
        );
        return adminUser;
      } else {
        Logger.info('🎯 NO ADMIN RECORD IN DATABASE FOR UID: $uid');
        return null;
      }
    } catch (e) {
      Logger.error('❌ DATABASE ERROR: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If the snapshot has user data, then they're already signed in
        if (snapshot.hasData && snapshot.data != null) {
          final user = snapshot.data!;
          final userUID = user.uid;
          final userEmail = user.email!;

          Logger.info(
            '👤 User authenticated: $userEmail (UID: $userUID) - Checking admin status...',
          );

          // ALWAYS check user role fresh from database using UID
          return FutureBuilder<AdminUser?>(
            key: ValueKey(
              'auth_${userUID}_${DateTime.now().millisecondsSinceEpoch}',
            ),
            future: _checkAdminStatus(userUID),
            builder: (context, adminSnapshot) {
              if (adminSnapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Checking user permissions...'),
                      ],
                    ),
                  ),
                );
              }

              // Check if user is an admin
              if (adminSnapshot.hasData && adminSnapshot.data != null) {
                final adminUser = adminSnapshot.data!;
                Logger.info(
                  '✅ ADMIN DETECTED: ${adminUser.email} - Role: ${adminUser.role.name}',
                );
                Logger.info('🔄 Redirecting to ADMIN SCREEN');

                // Set the current admin in the service
                AdminAuthService.setCurrentAdmin(adminUser);

                // Redirect to admin screen
                return const AdminScreen();
              }

              // Regular user - show categories screen
              Logger.info(
                '✅ REGULAR USER DETECTED - Redirecting to CATEGORIES SCREEN',
              );
              return const CategoriesScreen();
            },
          );
        }

        // Otherwise, they're not signed in
        return const LoginScreen();
      },
    );
  }
}
