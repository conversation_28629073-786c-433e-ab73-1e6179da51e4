import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'screens/login_screen.dart';
import 'screens/categories_screen.dart';
import 'screens/admin_screen.dart';
import 'services/admob_service.dart';
import 'utils/logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await AdMobService.initialize();
  await _initializeSuperAdmin();
  runApp(const MyApp());
}

/// Initialize super admin in database
Future<void> _initializeSuperAdmin() async {
  try {
    const superAdminEmail = '<EMAIL>';
    final emailKey = superAdminEmail.replaceAll('.', '_').replaceAll('@', '_');
    final ref = FirebaseDatabase.instance.ref('admins/$emailKey');
    final snapshot = await ref.once();

    if (!snapshot.snapshot.exists) {
      await ref.set({
        'email': superAdminEmail,
        'role': 'superAdmin',
        'addedAt': DateTime.now().millisecondsSinceEpoch,
        'addedBy': 'system',
      });
      Logger.info('Super admin initialized: $superAdminEmail');
    }
  } catch (e) {
    Logger.error('Failed to initialize super admin', e);
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wealth Vault',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/categories': (context) => const CategoriesScreen(),
      },
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  /// Simple admin check by email
  static Future<bool> _isUserAdmin(String email) async {
    try {
      Logger.info('🔍 Checking if $email is admin');

      // Convert email to database key
      final emailKey = email.replaceAll('.', '_').replaceAll('@', '_');

      // Check database
      final ref = FirebaseDatabase.instance.ref('admins/$emailKey');
      final snapshot = await ref.once();

      if (snapshot.snapshot.exists && snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        final role = data['role'] ?? '';
        Logger.info('✅ Admin found: $email - Role: $role');
        return true;
      } else {
        Logger.info('❌ Not an admin: $email');
        return false;
      }
    } catch (e) {
      Logger.error('❌ Error checking admin status: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If the snapshot has user data, then they're already signed in
        if (snapshot.hasData && snapshot.data != null) {
          final user = snapshot.data!;
          final userUID = user.uid;
          final userEmail = user.email!;

          Logger.info(
            '👤 User authenticated: $userEmail (UID: $userUID) - Checking admin status...',
          );

          // Check if user is admin
          return FutureBuilder<bool>(
            key: ValueKey(
              'admin_check_${user.uid}_${DateTime.now().millisecondsSinceEpoch}',
            ),
            future: _isUserAdmin(userEmail),
            builder: (context, adminSnapshot) {
              if (adminSnapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Checking user permissions...'),
                      ],
                    ),
                  ),
                );
              }

              // Check if user is admin
              if (adminSnapshot.data == true) {
                Logger.info('✅ ADMIN DETECTED - Redirecting to admin screen');
                return const AdminScreen();
              } else {
                Logger.info(
                  '✅ REGULAR USER - Redirecting to categories screen',
                );
                return const CategoriesScreen();
              }
            },
          );
        }

        // Otherwise, they're not signed in
        return const LoginScreen();
      },
    );
  }
}
