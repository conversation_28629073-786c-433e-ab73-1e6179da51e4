@charset "UTF-8";
/* CSS Document */

body {
	background-color: #ECEBEB;
	background-repeat: repeat;
	margin-top: 0px;
	}

p, td {
	font-family: verdana, arial, tahoma, sans-serif;
    color: #0A0A0A;
    font-size: 15px;
} 

#topribbon {
	background-image:url(images/topribbon.png);
}
#toprepeater {
	background-image:url(images/bg_header.jpg);
	background-repeat:repeat-x;
}
 
#header {
	background-image: url(images/header.png);
	height: 211px;
	width: 800px;
}

#body {
	background-image: url(images/body.png);
	background-repeat: repeat-y;
	height: auto;
	width: 800px;
}

h1 {
    letter-spacing: -1;
	font-family: Tahoma, Verdana, Arial, serif;
	font-size:30pt;
	font-weight:bold;
	text-align:center;
	color:#990000;	

}
h2 {
    letter-spacing: -1;
	font-size: 24pt;
	font-weight: bold;
	color: #222222;
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, serif;
}

h3 {
	letter-spacing: -1;
	font-size: 18pt;
	font-weight: bold;
	color: #222222;
    font-family: <PERSON><PERSON><PERSON>, <PERSON>erd<PERSON>, <PERSON><PERSON>, serif;
}

h4 {
	letter-spacing: -1;
	font-size: 18pt;
	font-weight: bold;
	color: #ce0000;
    font-family: Tahoma, Verdana, Arial, serif;
}

h5 {
	font-size: 30pt;
	color: #ffffff;
    font-family: Impact, serif;
}

.highlight {
	font-weight: bold;
	background-color: #f7f903;
}

.table1 {
	 border-collapse: collapse;
	 border: 1px dashed rgb(169, 169, 169); 
	 padding: 10px;
	
	}
	
.table2 { border-collapse: collapse; padding: 10px; border: solid 1px silver; }
	
.table3 {
	 border-collapse: collapse;
	 border: 1px solid rgb(169, 169, 169); 
	 padding: 10px;
	}

.orderform {
	 padding: 20px;
	 background-color: FFFFCC;
	 border-collapse: collapse;
	 border: dashed 3px red; 
	 }


.testimonial {
	margin: 5px 10px 5px 10px;
	padding: 10px 60px 10px 10px;
	border-width: 2px;
	border-style: dashed;
	border-color: #cfcfcf;
	background-color: #fafcf0;
	background-image: url(images/quote.png);
	background-repeat: no-repeat;
	background-position: top right;
	font-style:italic;
	color: #8f8f8f;
	}
	
	.number1 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/1Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
		.number2 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/2Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number3 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/3Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number4 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/4Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number5 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/5Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number6 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/6Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number7{
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/7Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number8 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/8Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
			.number9 {
	margin: 5px 10px 5px 10px;
	padding: 0px 0px 10px 65px;
	background-image: url(images/numbers/9Orange.png);
	background-repeat: no-repeat;
	background-position: top left;
	font-style:italic;
	color: #8f8f8f;
	}
	
ul {
}
li {
	list-style:url(images/checkmarkOrange.png);
	margin-bottom:15px;
	line-height:20px;
}
.xmark ul {
}
.xmark li {
	list-style:url(images/xmark.png);
	margin-bottom:15px;
	line-height:20px;
}
.checkmarkBlue1 ul {
}
.checkmarkBlue1 li {
	list-style:url(images/checkmarkBlue1.png);
	margin-bottom:15px;
	line-height:20px;
}
}
.checkmarkBlue2 ul {
}
.checkmarkBlue2 li {
	list-style:url(images/checkmarkBlue2.png);
	margin-bottom:15px;
	line-height:20px;
}
}
.checkmarkRed ul {
}
.checkmarkRed li {
	list-style:url(images/checkmarkRed.png);
	margin-bottom:15px;
	line-height:20px;
}
}
.checkmarkGreen ul {
}
.checkmarkGreen li {
	list-style:url(images/checkmarkGreen.png);
	margin-bottom:15px;
	line-height:20px;
}
}
.checkmarkBlack ul {
}
.checkmarkBlack li {
	list-style:url(images/checkmarkBlack.png);
	margin-bottom:15px;
	line-height:20px;
}