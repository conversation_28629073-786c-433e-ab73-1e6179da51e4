import 'package:flutter/material.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import '../models/content_item.dart';
import '../services/realtime_database_service.dart';
import '../services/firebase_storage_service.dart';
import '../utils/logger.dart';

class AddContentScreen extends StatefulWidget {
  final VoidCallback onContentAdded;

  const AddContentScreen({
    super.key,
    required this.onContentAdded,
  });

  @override
  State<AddContentScreen> createState() => _AddContentScreenState();
}

class _AddContentScreenState extends State<AddContentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();
  final FirebaseStorageService _storageService = FirebaseStorageService();

  String? _selectedCategory;
  File? _selectedFile;
  bool _isUploading = false;
  double _uploadProgress = 0;

  final List<String> _categories = [
    'Business & Entrepreneurship',
    'Health & Wellness',
    'Technology & Innovation',
    'Recipes & Nutrition',
    'MRR Video Courses',
    'Finance & Investment',
    'Self-Improvement & Motivation',
    'Marketing & Branding',
    'Design & Templates',
    'Spirituality & Mindfulness',
    'Career & Freelancing',
    'AI & Automation',
    'Education & eLearning',
    'Legal & Business Docs',
    'eCommerce & Dropshipping',
    'Parenting & Family',
    'Fashion & Beauty',
    'Travel & Lifestyle',
    'Kids & Learning',
    'Entertainment & Fun',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          _selectedFile = file;
          // Auto-fill title if empty
          if (_titleController.text.isEmpty) {
            final fileName = path.basenameWithoutExtension(file.path);
            _titleController.text = fileName.replaceAll('_', ' ').replaceAll('-', ' ');
          }
        });
      }
    } catch (e) {
      Logger.error('Failed to pick file', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick file: $e')),
        );
      }
    }
  }

  Future<void> _uploadContent() async {
    if (!_formKey.currentState!.validate() || _selectedFile == null || _selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill all fields and select a file')),
      );
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0;
    });

    try {
      // Upload file to Firebase Storage
      final fileName = path.basename(_selectedFile!.path);
      final downloadUrl = await _storageService.uploadFile(
        file: _selectedFile!,
        category: _selectedCategory!,
        fileName: fileName,
        onProgress: (progress) {
          setState(() => _uploadProgress = progress);
        },
      );

      // Create content item
      final contentItem = ContentItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory!,
        fileUrl: downloadUrl,
        fileName: fileName,
        fileType: path.extension(fileName).substring(1).toLowerCase(),
        fileSize: await _selectedFile!.length(),
        createdAt: DateTime.now(),
      );

      // Add to database
      await _dbService.addContentItem(contentItem);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Content uploaded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onContentAdded();
        Navigator.pop(context);
      }
    } catch (e) {
      Logger.error('Failed to upload content', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload content: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New Content'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Category selection
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: 'Category *',
                  border: OutlineInputBorder(),
                ),
                items: _categories.map((category) => DropdownMenuItem(
                  value: category,
                  child: Text(category),
                )).toList(),
                onChanged: (value) => setState(() => _selectedCategory = value),
                validator: (value) => value == null ? 'Please select a category' : null,
              ),
              const SizedBox(height: 16),

              // Title field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Title *',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) => value?.trim().isEmpty == true ? 'Please enter a title' : null,
              ),
              const SizedBox(height: 16),

              // Description field
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  border: OutlineInputBorder(),
                ),
                maxLines: 4,
                validator: (value) => value?.trim().isEmpty == true ? 'Please enter a description' : null,
              ),
              const SizedBox(height: 16),

              // File selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.attach_file),
                          const SizedBox(width: 8),
                          const Text(
                            'File Selection *',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const Spacer(),
                          ElevatedButton.icon(
                            onPressed: _isUploading ? null : _pickFile,
                            icon: const Icon(Icons.folder_open),
                            label: const Text('Browse'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (_selectedFile != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.insert_drive_file, color: Colors.blue),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      path.basename(_selectedFile!.path),
                                      style: const TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                    FutureBuilder<int>(
                                      future: _selectedFile!.length(),
                                      builder: (context, snapshot) {
                                        if (snapshot.hasData) {
                                          final size = snapshot.data!;
                                          final sizeStr = size < 1024 * 1024
                                              ? '${(size / 1024).toStringAsFixed(1)} KB'
                                              : '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
                                          return Text(
                                            sizeStr,
                                            style: const TextStyle(color: Colors.grey),
                                          );
                                        }
                                        return const Text('Calculating size...');
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                onPressed: _isUploading ? null : () => setState(() => _selectedFile = null),
                                icon: const Icon(Icons.close, color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ] else ...[
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Center(
                            child: Text(
                              'No file selected',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Upload progress
              if (_isUploading) ...[
                LinearProgressIndicator(value: _uploadProgress),
                const SizedBox(height: 8),
                Text(
                  'Uploading... ${(_uploadProgress * 100).toStringAsFixed(1)}%',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
              ],

              // Upload button
              ElevatedButton(
                onPressed: _isUploading ? null : _uploadContent,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isUploading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text('Uploading...'),
                        ],
                      )
                    : const Text(
                        'Upload Content',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
