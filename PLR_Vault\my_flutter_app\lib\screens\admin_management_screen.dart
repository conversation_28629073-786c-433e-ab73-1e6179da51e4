import 'package:flutter/material.dart';
import '../services/admin_auth_service.dart';
import '../utils/logger.dart';

class AdminManagementScreen extends StatefulWidget {
  const AdminManagementScreen({super.key});

  @override
  State<AdminManagementScreen> createState() => _AdminManagementScreenState();
}

class _AdminManagementScreenState extends State<AdminManagementScreen> {
  List<AdminUser> _admins = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAdmins();
  }

  Future<void> _loadAdmins() async {
    try {
      setState(() => _isLoading = true);
      final admins = await AdminAuthService.getAllAdmins();
      setState(() {
        _admins = admins;
        _isLoading = false;
      });
    } catch (e) {
      Logger.error('Failed to load admins', e);
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load admins: $e')),
        );
      }
    }
  }

  Future<void> _addAdmin() async {
    final emailController = TextEditingController();
    AdminRole selectedRole = AdminRole.admin;

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Add New Admin'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: emailController,
                decoration: const InputDecoration(
                  labelText: 'Email Address',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<AdminRole>(
                value: selectedRole,
                decoration: const InputDecoration(
                  labelText: 'Role',
                  border: OutlineInputBorder(),
                ),
                items: AdminRole.values.map((role) => DropdownMenuItem(
                  value: role,
                  child: Text(role == AdminRole.superAdmin ? 'Super Admin' : 'Admin'),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setDialogState(() => selectedRole = value);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (emailController.text.trim().isNotEmpty) {
                  Navigator.pop(context, {
                    'email': emailController.text.trim(),
                    'role': selectedRole,
                  });
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      try {
        await AdminAuthService.addAdmin(result['email'], result['role']);
        await _loadAdmins(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Admin added successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to add admin: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    emailController.dispose();
  }

  Future<void> _removeAdmin(AdminUser admin) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Admin'),
        content: Text('Are you sure you want to remove "${admin.email}" as an admin?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AdminAuthService.removeAdmin(admin.email);
        await _loadAdmins(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Admin removed successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to remove admin: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Management'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _addAdmin,
            icon: const Icon(Icons.person_add),
            tooltip: 'Add Admin',
          ),
          IconButton(
            onPressed: _loadAdmins,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _admins.isEmpty
              ? const Center(
                  child: Text(
                    'No admins found',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _admins.length,
                  itemBuilder: (context, index) {
                    final admin = _admins[index];
                    final isSuperAdmin = admin.role == AdminRole.superAdmin;
                    final isCurrentUser = admin.email == AdminAuthService.adminEmail;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: isSuperAdmin ? Colors.red : Colors.blue,
                          child: Icon(
                            isSuperAdmin ? Icons.admin_panel_settings : Icons.person,
                            color: Colors.white,
                          ),
                        ),
                        title: Text(
                          admin.email,
                          style: TextStyle(
                            fontWeight: isCurrentUser ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isSuperAdmin ? 'Super Admin' : 'Admin',
                              style: TextStyle(
                                color: isSuperAdmin ? Colors.red : Colors.blue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Added: ${admin.addedAt.day}/${admin.addedAt.month}/${admin.addedAt.year}',
                              style: const TextStyle(fontSize: 12, color: Colors.grey),
                            ),
                            if (admin.addedBy != 'system')
                              Text(
                                'Added by: ${admin.addedBy}',
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            if (isCurrentUser)
                              const Text(
                                '(You)',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                        trailing: !isSuperAdmin
                            ? IconButton(
                                onPressed: () => _removeAdmin(admin),
                                icon: const Icon(Icons.delete, color: Colors.red),
                                tooltip: 'Remove Admin',
                              )
                            : const Icon(Icons.lock, color: Colors.grey),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addAdmin,
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        child: const Icon(Icons.person_add),
      ),
    );
  }
}
