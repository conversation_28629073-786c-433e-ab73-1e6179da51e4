import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;

class FirebaseStorageService {
  static final FirebaseStorageService _instance =
      FirebaseStorageService._internal();
  factory FirebaseStorageService() => _instance;
  FirebaseStorageService._internal();

  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload a file to Firebase Storage
  /// Returns the download URL of the uploaded file
  Future<String> uploadFile({
    required File file,
    required String category,
    required String fileName,
    Function(double)? onProgress,
  }) async {
    try {
      // Check if file exists
      if (!await file.exists()) {
        throw Exception('File does not exist: ${file.path}');
      }

      // Create a reference to the file location
      final String filePath = 'content/$category/$fileName';
      final Reference ref = _storage.ref().child(filePath);

      // Create upload task with metadata
      final SettableMetadata metadata = SettableMetadata(
        contentType: _getContentType(fileName),
        customMetadata: {
          'category': category,
          'uploadedAt': DateTime.now().toIso8601String(),
        },
      );

      final UploadTask uploadTask = ref.putFile(file, metadata);

      // Listen to upload progress if callback provided
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          if (snapshot.totalBytes > 0) {
            final double progress =
                snapshot.bytesTransferred / snapshot.totalBytes;
            onProgress(progress);
          }
        });
      }

      // Wait for upload to complete
      final TaskSnapshot snapshot = await uploadTask;

      // Get download URL
      final String downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      if (e.toString().contains('permission-denied')) {
        throw Exception(
          'Permission denied. Please check Firebase Storage rules.',
        );
      } else if (e.toString().contains('network')) {
        throw Exception(
          'Network error. Please check your internet connection.',
        );
      } else {
        throw Exception('Failed to upload file: $e');
      }
    }
  }

  /// Get content type for file
  String _getContentType(String fileName) {
    final String extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'mp4':
        return 'video/mp4';
      case 'txt':
        return 'text/plain';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'mov':
        return 'video/quicktime';
      case 'avi':
        return 'video/x-msvideo';
      case 'mkv':
        return 'video/x-matroska';
      default:
        return 'application/octet-stream';
    }
  }

  /// Upload multiple files to Firebase Storage
  /// Returns a map of fileName -> downloadUrl
  Future<Map<String, String>> uploadMultipleFiles({
    required List<File> files,
    required String category,
    Function(String fileName, double progress)? onProgress,
  }) async {
    final Map<String, String> uploadResults = {};

    for (final File file in files) {
      try {
        final String fileName = path.basename(file.path);
        final String downloadUrl = await uploadFile(
          file: file,
          category: category,
          fileName: fileName,
          onProgress: onProgress != null
              ? (progress) => onProgress(fileName, progress)
              : null,
        );
        uploadResults[fileName] = downloadUrl;
      } catch (e) {
        // Continue with other files even if one fails
      }
    }

    return uploadResults;
  }

  /// Delete a file from Firebase Storage
  Future<void> deleteFile(String fileUrl) async {
    try {
      final Reference ref = _storage.refFromURL(fileUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('Failed to delete file: $e');
    }
  }

  /// Get file metadata
  Future<FullMetadata> getFileMetadata(String fileUrl) async {
    try {
      final Reference ref = _storage.refFromURL(fileUrl);
      return await ref.getMetadata();
    } catch (e) {
      throw Exception('Failed to get file metadata: $e');
    }
  }

  /// List all files in a category
  Future<List<Reference>> listFilesInCategory(String category) async {
    try {
      final Reference ref = _storage.ref().child('content/$category');
      final ListResult result = await ref.listAll();
      return result.items;
    } catch (e) {
      throw Exception('Failed to list files in category: $e');
    }
  }

  /// Get all categories (folders) in content
  Future<List<String>> getCategories() async {
    try {
      final Reference ref = _storage.ref().child('content');
      final ListResult result = await ref.listAll();
      return result.prefixes.map((ref) => ref.name).toList();
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  /// Check if file exists in storage
  Future<bool> fileExists(String fileUrl) async {
    try {
      final Reference ref = _storage.refFromURL(fileUrl);
      await ref.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get file size from URL
  Future<int> getFileSize(String fileUrl) async {
    try {
      final FullMetadata metadata = await getFileMetadata(fileUrl);
      return metadata.size ?? 0;
    } catch (e) {
      return 0;
    }
  }
}
