{"logs": [{"outputFile": "com.plrvault.app-mergeReleaseResources-42:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5aa58ad971ef0b034990717d52ef9e6\\transformed\\jetified-play-services-ads-23.6.0\\res\\values\\values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "697,698,797,800,803,806,808,810,812,814,816,818,820,822,824,826,829,830,831,832,833,834,835,838", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "36011,36071,43815,44071,44329,44614,44757,44918,45134,45342,45574,45802,46069,46244,46431,46654,46915,46957,47034,47072,47111,47164,47232,47395", "endLines": "697,698,799,802,805,807,809,811,813,815,817,819,821,823,825,827,829,830,831,832,833,834,835,840", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "36066,36116,44066,44324,44609,44752,44913,45129,45337,45569,45797,46064,46239,46426,46649,46838,46952,47029,47067,47106,47159,47227,47266,47559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7976d4e64729cb9c47971e21b0850b04\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "450,451,452,453,454,455,456,457,768,769,770,771,772,773,774,775,777,778,779,780,781,782,783,784,785,4120,4513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20003,20093,20173,20263,20353,20433,20514,20594,40418,40523,40704,40829,40936,41116,41239,41355,41625,41813,41918,42099,42224,42399,42547,42610,42672,215529,229879", "endLines": "450,451,452,453,454,455,456,457,768,769,770,771,772,773,774,775,777,778,779,780,781,782,783,784,785,4132,4531", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "20088,20168,20258,20348,20428,20509,20589,20669,40518,40699,40824,40931,41111,41234,41350,41453,41808,41913,42094,42219,42394,42542,42605,42667,42746,215839,230291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "10,66,72,80,338,350,356,362,363,364,365,366,691,2475,2481,4544,4552,4567", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "445,2923,3096,3315,14685,14999,15187,15374,15427,15487,15539,15584,35749,154353,154548,230608,230890,231504", "endLines": "10,71,79,87,349,355,361,362,363,364,365,366,691,2480,2485,4551,4566,4582", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "499,3091,3310,3529,14994,15182,15369,15422,15482,15534,15579,15618,35804,154543,154701,230885,231499,232153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "731,776", "startColumns": "4,4", "startOffsets": "37820,41458", "endColumns": "67,166", "endOffsets": "37883,41620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f43a8a95ab2f768e2215dacb56c18080\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,11,16,17,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,43,44,45,46,56,65,88,89,94,95,100,105,106,107,112,113,118,119,124,125,126,132,133,134,139,145,146,149,150,156,157,158,159,162,165,168,169,172,175,176,177,178,179,182,185,186,187,188,194,199,202,205,206,207,212,213,214,217,220,221,224,227,230,233,234,235,238,241,242,247,248,254,259,262,265,266,267,268,269,270,271,272,273,274,275,276,292,298,299,300,301,303,310,316,317,318,321,326,327,335,336,337,367,368,369,371,372,381,382,383,389,390,396,400,401,402,403,404,413,696,721,3040,3124,3289,3417,3423,3427,3576,3721,3854,3870,3895,3918,3921,3924,3927,3954,3981,3998,4298,4306,4319,4335,4339,4369,4382,4386,4396,4406,4460,4583,4607,4699,4770,4807,4842,4894,4911", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,389,504,690,751,1042,1094,1144,1197,1245,1296,1351,1411,1476,1535,1597,1649,1710,1772,1818,1951,2003,2053,2104,2511,2878,3534,3593,3790,3847,4042,4223,4277,4334,4526,4584,4780,4836,5030,5087,5138,5360,5412,5467,5657,5873,5923,6069,6125,6331,6392,6452,6522,6655,6786,6914,6982,7111,7237,7299,7362,7430,7497,7620,7745,7812,7877,7942,8231,8412,8533,8654,8720,8787,8997,9066,9132,9257,9383,9450,9576,9703,9828,9955,10011,10076,10202,10325,10390,10598,10665,10953,11133,11253,11373,11438,11500,11562,11626,11688,11747,11807,11868,11929,11988,12048,12708,12959,13010,13059,13107,13225,13517,13747,13794,13854,13960,14140,14194,14529,14583,14639,15623,15670,15721,15821,15873,16203,16262,16316,16554,16609,16811,16950,16996,17051,17096,17140,17488,35970,37261,172695,176660,182778,188443,188818,188985,194215,200341,205190,205941,206795,207665,207731,207810,207885,208669,209560,210379,221107,221512,221983,222774,222937,224298,224862,225015,225474,225892,228285,232158,232846,236859,239637,240380,241801,244008,244750", "endLines": "8,9,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,65,88,93,94,99,104,105,106,111,112,117,118,123,124,125,131,132,133,138,144,145,146,149,155,156,157,158,161,164,167,168,171,174,175,176,177,178,181,184,185,186,187,193,198,201,204,205,206,211,212,213,216,219,220,223,226,229,232,233,234,237,240,241,246,247,253,258,261,264,265,266,267,268,269,270,271,272,273,274,275,291,297,298,299,300,301,309,315,316,317,320,325,326,334,335,336,337,367,368,369,371,380,381,382,388,389,395,399,400,401,402,403,412,416,696,721,3057,3288,3416,3422,3426,3575,3720,3733,3869,3894,3917,3920,3923,3926,3953,3980,3997,4083,4305,4318,4334,4338,4368,4381,4385,4395,4405,4449,4471,4586,4609,4714,4806,4841,4848,4910,4913", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "384,440,685,746,1037,1089,1139,1192,1240,1291,1346,1406,1471,1530,1592,1644,1705,1767,1813,1946,1998,2048,2099,2506,2818,2918,3588,3785,3842,4037,4218,4272,4329,4521,4579,4775,4831,5025,5082,5133,5355,5407,5462,5652,5868,5918,5970,6120,6326,6387,6447,6517,6650,6781,6909,6977,7106,7232,7294,7357,7425,7492,7615,7740,7807,7872,7937,8226,8407,8528,8649,8715,8782,8992,9061,9127,9252,9378,9445,9571,9698,9823,9950,10006,10071,10197,10320,10385,10593,10660,10948,11128,11248,11368,11433,11495,11557,11621,11683,11742,11802,11863,11924,11983,12043,12703,12954,13005,13054,13102,13160,13512,13742,13789,13849,13955,14135,14189,14524,14578,14634,14680,15665,15716,15775,15868,16198,16257,16311,16549,16604,16806,16945,16991,17046,17091,17135,17483,17620,36006,37301,173627,182773,188438,188813,188980,194210,200336,201033,205936,206790,207660,207726,207805,207880,208664,209555,210374,214311,221507,221978,222769,222932,224293,224857,225010,225469,225887,227900,228576,232303,232961,237501,240375,241796,242149,244745,244846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4a06d06ec0ce1c73cc8df2ae11f0f21f\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "64,147,148,370,417,418,423,424,425,426,427,428,429,432,433,434,435,436,437,438,439,440,441,446,447,458,459,460,461,462,463,464,465,476,477,478,479,480,481,482,483,484,485,486,487,488,489,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,602,603,622,623,624,625,626,627,628,644,645,646,647,648,649,650,651,687,688,689,690,693,699,700,703,720,727,728,729,730,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,836,841,842,843,844,845,846,854,855,859,863,867,872,878,885,889,893,898,902,906,910,914,918,922,928,932,938,942,948,952,957,961,964,968,974,978,984,988,994,997,1001,1005,1009,1013,1017,1018,1019,1020,1023,1026,1029,1032,1036,1037,1038,1039,1040,1043,1045,1047,1049,1054,1055,1059,1065,1069,1070,1072,1084,1085,1089,1095,1099,1100,1101,1105,1132,1136,1137,1141,1169,1341,1367,1538,1564,1595,1603,1609,1625,1647,1652,1657,1667,1676,1685,1689,1696,1715,1722,1723,1732,1735,1738,1742,1746,1750,1753,1754,1759,1764,1774,1779,1786,1792,1793,1796,1800,1805,1807,1809,1812,1815,1817,1821,1824,1831,1834,1837,1841,1843,1847,1849,1851,1853,1857,1865,1873,1885,1891,1900,1903,1914,1917,1918,1923,1924,1936,2005,2075,2076,2086,2095,2096,2098,2102,2105,2108,2111,2114,2117,2120,2123,2127,2130,2133,2136,2140,2143,2147,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2173,2175,2176,2177,2178,2179,2180,2181,2182,2184,2185,2187,2188,2190,2192,2193,2195,2196,2197,2198,2199,2200,2202,2203,2204,2205,2206,2218,2220,2222,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2238,2239,2240,2241,2242,2243,2244,2246,2250,2262,2263,2264,2265,2266,2267,2271,2272,2273,2274,2276,2278,2280,2282,2284,2285,2286,2287,2289,2291,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2307,2308,2309,2310,2312,2314,2315,2317,2318,2320,2322,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2337,2338,2339,2340,2342,2343,2344,2345,2346,2348,2350,2352,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2371,2446,2449,2452,2455,2469,2493,2535,2538,2567,2594,2603,2667,3030,3058,3096,3734,4084,4108,4114,4133,4154,4278,4450,4456,4472,4478,4532,4610,4679,4715,4849,4861,4887", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2823,5975,6020,15780,17625,17680,18002,18066,18136,18197,18272,18348,18425,18663,18748,18830,18906,18982,19059,19137,19243,19349,19428,19757,19814,20674,20748,20823,20888,20954,21014,21075,21147,21699,21766,21834,21893,21952,22011,22070,22129,22183,22237,22290,22344,22398,22452,22638,22712,22791,22864,22938,23009,23081,23153,23226,23283,23341,23414,23488,23562,23637,23709,23782,23852,23923,23983,24044,24113,24182,24252,24326,24402,24466,24543,24619,24696,24761,24830,24907,24982,25051,25119,25196,25262,25323,25420,25485,25554,25653,25724,25783,25841,25898,25957,26021,26092,26164,26236,26308,26380,26447,26515,26583,26642,26705,26769,26859,26950,27010,27076,27143,27209,27279,27343,27396,27463,27524,27591,27704,27762,27825,27890,27955,28030,28103,28175,28219,28266,28312,28361,28422,28483,28544,28606,28670,28734,28798,28863,28926,28986,29047,29113,29172,29232,29294,29365,29425,30124,30210,31379,31469,31556,31644,31726,31809,31899,32968,33020,33078,33123,33189,33253,33310,33367,35544,35601,35649,35698,35866,36121,36168,36324,37229,37577,37641,37703,37763,37958,38032,38102,38180,38234,38304,38389,38437,38483,38544,38607,38673,38737,38808,38871,38936,39000,39061,39122,39174,39247,39321,39390,39465,39539,39613,39754,47271,47564,47642,47732,47820,47916,48006,48588,48677,48924,49205,49457,49742,50135,50612,50834,51056,51332,51559,51789,52019,52249,52479,52706,53125,53351,53776,54006,54434,54653,54936,55144,55275,55502,55928,56153,56580,56801,57226,57346,57622,57923,58247,58538,58852,58989,59120,59225,59467,59634,59838,60046,60317,60429,60541,60646,60763,60977,61123,61263,61349,61697,61785,62031,62449,62698,62780,62878,63535,63635,63887,64311,64566,64660,64749,64986,67010,67252,67354,67607,69763,80444,81960,92655,94183,95940,96566,96986,98247,99512,99768,100004,100551,101045,101650,101848,102428,103796,104171,104289,104827,104984,105180,105453,105709,105879,106020,106084,106449,106816,107492,107756,108094,108447,108541,108727,109033,109295,109420,109547,109786,109997,110116,110309,110486,110941,111122,111244,111503,111616,111803,111905,112012,112141,112416,112924,113420,114297,114591,115161,115310,116042,116214,116298,116634,116726,117354,122585,127956,128018,128596,129180,129271,129384,129613,129773,129925,130096,130262,130431,130598,130761,131004,131174,131347,131518,131792,131991,132196,132526,132610,132706,132802,132900,133000,133102,133204,133306,133408,133510,133610,133706,133818,133947,134070,134201,134332,134430,134544,134638,134778,134912,135008,135120,135220,135336,135432,135544,135644,135784,135920,136084,136214,136372,136522,136663,136807,136942,137054,137204,137332,137460,137596,137728,137858,137988,138100,138998,139144,139288,139426,139492,139582,139658,139762,139852,139954,140062,140170,140270,140350,140442,140540,140650,140702,140780,140886,140978,141082,141192,141314,141477,142032,142112,142212,142302,142412,142502,142743,142837,142943,143035,143135,143247,143361,143477,143593,143687,143801,143913,144015,144135,144257,144339,144443,144563,144689,144787,144881,144969,145081,145197,145319,145431,145606,145722,145808,145900,146012,146136,146203,146329,146397,146525,146669,146797,146866,146961,147076,147189,147288,147397,147508,147619,147720,147825,147925,148055,148146,148269,148363,148475,148561,148665,148761,148849,148967,149071,149175,149301,149389,149497,149597,149687,149797,149881,149983,150067,150121,150185,150291,150377,150487,150571,150830,153446,153564,153679,153759,154120,154886,156290,156368,157712,159073,159461,162304,172357,173632,175303,201038,214316,215067,215329,215844,216223,220501,227905,228134,228581,228796,230296,232966,236115,237506,242154,242494,243805", "endLines": "64,147,148,370,417,418,423,424,425,426,427,428,429,432,433,434,435,436,437,438,439,440,441,446,447,458,459,460,461,462,463,464,465,476,477,478,479,480,481,482,483,484,485,486,487,488,489,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,602,603,622,623,624,625,626,627,628,644,645,646,647,648,649,650,651,687,688,689,690,693,699,700,703,720,727,728,729,730,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,836,841,842,843,844,845,853,854,858,862,866,871,877,884,888,892,897,901,905,909,913,917,921,927,931,937,941,947,951,956,960,963,967,973,977,983,987,993,996,1000,1004,1008,1012,1016,1017,1018,1019,1022,1025,1028,1031,1035,1036,1037,1038,1039,1042,1044,1046,1048,1053,1054,1058,1064,1068,1069,1071,1083,1084,1088,1094,1098,1099,1100,1104,1131,1135,1136,1140,1168,1340,1366,1537,1563,1594,1602,1608,1624,1646,1651,1656,1666,1675,1684,1688,1695,1714,1721,1722,1731,1734,1737,1741,1745,1749,1752,1753,1758,1763,1773,1778,1785,1791,1792,1795,1799,1804,1806,1808,1811,1814,1816,1820,1823,1830,1833,1836,1840,1842,1846,1848,1850,1852,1856,1864,1872,1884,1890,1899,1902,1913,1916,1917,1922,1923,1928,2004,2074,2075,2085,2094,2095,2097,2101,2104,2107,2110,2113,2116,2119,2122,2126,2129,2132,2135,2139,2142,2146,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2172,2174,2175,2176,2177,2178,2179,2180,2181,2183,2184,2186,2187,2189,2191,2192,2194,2195,2196,2197,2198,2199,2201,2202,2203,2204,2205,2206,2219,2221,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2237,2238,2239,2240,2241,2242,2243,2245,2249,2253,2262,2263,2264,2265,2266,2270,2271,2272,2273,2275,2277,2279,2281,2283,2284,2285,2286,2288,2290,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2306,2307,2308,2309,2311,2313,2314,2316,2317,2319,2321,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2336,2337,2338,2339,2341,2342,2343,2344,2345,2347,2349,2351,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2445,2448,2451,2454,2468,2474,2502,2537,2566,2593,2602,2666,3029,3033,3085,3123,3751,4107,4113,4119,4153,4277,4297,4455,4459,4477,4512,4543,4675,4698,4769,4860,4886,4893", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2873,6015,6064,15816,17675,17737,18061,18131,18192,18267,18343,18420,18498,18743,18825,18901,18977,19054,19132,19238,19344,19423,19503,19809,19867,20743,20818,20883,20949,21009,21070,21142,21215,21761,21829,21888,21947,22006,22065,22124,22178,22232,22285,22339,22393,22447,22501,22707,22786,22859,22933,23004,23076,23148,23221,23278,23336,23409,23483,23557,23632,23704,23777,23847,23918,23978,24039,24108,24177,24247,24321,24397,24461,24538,24614,24691,24756,24825,24902,24977,25046,25114,25191,25257,25318,25415,25480,25549,25648,25719,25778,25836,25893,25952,26016,26087,26159,26231,26303,26375,26442,26510,26578,26637,26700,26764,26854,26945,27005,27071,27138,27204,27274,27338,27391,27458,27519,27586,27699,27757,27820,27885,27950,28025,28098,28170,28214,28261,28307,28356,28417,28478,28539,28601,28665,28729,28793,28858,28921,28981,29042,29108,29167,29227,29289,29360,29420,29488,30205,30292,31464,31551,31639,31721,31804,31894,31985,33015,33073,33118,33184,33248,33305,33362,33416,35596,35644,35693,35744,35895,36163,36212,36365,37256,37636,37698,37758,37815,38027,38097,38175,38229,38299,38384,38432,38478,38539,38602,38668,38732,38803,38866,38931,38995,39056,39117,39169,39242,39316,39385,39460,39534,39608,39749,39819,47319,47637,47727,47815,47911,48001,48583,48672,48919,49200,49452,49737,50130,50607,50829,51051,51327,51554,51784,52014,52244,52474,52701,53120,53346,53771,54001,54429,54648,54931,55139,55270,55497,55923,56148,56575,56796,57221,57341,57617,57918,58242,58533,58847,58984,59115,59220,59462,59629,59833,60041,60312,60424,60536,60641,60758,60972,61118,61258,61344,61692,61780,62026,62444,62693,62775,62873,63530,63630,63882,64306,64561,64655,64744,64981,67005,67247,67349,67602,69758,80439,81955,92650,94178,95935,96561,96981,98242,99507,99763,99999,100546,101040,101645,101843,102423,103791,104166,104284,104822,104979,105175,105448,105704,105874,106015,106079,106444,106811,107487,107751,108089,108442,108536,108722,109028,109290,109415,109542,109781,109992,110111,110304,110481,110936,111117,111239,111498,111611,111798,111900,112007,112136,112411,112919,113415,114292,114586,115156,115305,116037,116209,116293,116629,116721,116999,122580,127951,128013,128591,129175,129266,129379,129608,129768,129920,130091,130257,130426,130593,130756,130999,131169,131342,131513,131787,131986,132191,132521,132605,132701,132797,132895,132995,133097,133199,133301,133403,133505,133605,133701,133813,133942,134065,134196,134327,134425,134539,134633,134773,134907,135003,135115,135215,135331,135427,135539,135639,135779,135915,136079,136209,136367,136517,136658,136802,136937,137049,137199,137327,137455,137591,137723,137853,137983,138095,138235,139139,139283,139421,139487,139577,139653,139757,139847,139949,140057,140165,140265,140345,140437,140535,140645,140697,140775,140881,140973,141077,141187,141309,141472,141629,142107,142207,142297,142407,142497,142738,142832,142938,143030,143130,143242,143356,143472,143588,143682,143796,143908,144010,144130,144252,144334,144438,144558,144684,144782,144876,144964,145076,145192,145314,145426,145601,145717,145803,145895,146007,146131,146198,146324,146392,146520,146664,146792,146861,146956,147071,147184,147283,147392,147503,147614,147715,147820,147920,148050,148141,148264,148358,148470,148556,148660,148756,148844,148962,149066,149170,149296,149384,149492,149592,149682,149792,149876,149978,150062,150116,150180,150286,150372,150482,150566,150686,153441,153559,153674,153754,154115,154348,155398,156363,157707,159068,159456,162299,172352,172487,174997,176655,201605,215062,215324,215524,216218,220496,221102,228129,228280,228791,229874,230603,235987,236854,239632,242489,243800,244003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "760", "startColumns": "4", "startOffsets": "39824", "endColumns": "82", "endOffsets": "39902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fc83ed23dd70c2212cc258f35335ac1d\\transformed\\jetified-play-services-ads-lite-23.6.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "2254,2486", "startColumns": "4,4", "startOffsets": "141634,154706", "endLines": "2261,2492", "endColumns": "8,20", "endOffsets": "142027,154881"}}, {"source": "E:\\Fiverr_Projects\\PLR_Vault\\my_flutter_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1929,1933", "startColumns": "4,4", "startOffsets": "117004,117185", "endLines": "1932,1935", "endColumns": "12,12", "endOffsets": "117180,117349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f2d61afc8b21a772bd0b7bc042cb3cc\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "419,420,421,422", "startColumns": "4,4,4,4", "startOffsets": "17742,17807,17877,17941", "endColumns": "64,69,63,60", "endOffsets": "17802,17872,17936,17997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "442,443,444,445,593,594,786,788,789,790", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19508,19566,19632,19695,29493,29564,42751,42963,43030,43109", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "19561,19627,19690,19752,29559,29631,42814,43025,43104,43173"}}, {"source": "E:\\Fiverr_Projects\\PLR_Vault\\my_flutter_app\\build\\app\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,199,318,400,504,613,733,836", "endColumns": "143,118,81,103,108,119,102,71", "endOffsets": "194,313,395,499,608,728,831,903"}, "to": {"startLines": "787,791,792,793,794,795,796,828", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "42819,43178,43297,43379,43483,43592,43712,46843", "endColumns": "143,118,81,103,108,119,102,71", "endOffsets": "42958,43292,43374,43478,43587,43707,43810,46910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "722", "startColumns": "4", "startOffsets": "37306", "endColumns": "42", "endOffsets": "37344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "302,430,431,448,449,490,491,595,596,597,598,599,600,601,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,694,695,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,732,761,762,763,764,765,766,767,837,2207,2208,2212,2213,2217,2369,2370,3034,3086,3752,3785,3815,3848", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13165,18503,18575,19872,19937,22506,22575,29636,29706,29774,29846,29916,29977,30051,31990,32051,32112,32174,32238,32300,32361,32429,32529,32589,32655,32728,32797,32854,32906,33421,33493,33569,33634,33693,33752,33812,33872,33932,33992,34052,34112,34172,34232,34292,34352,34411,34471,34531,34591,34651,34711,34771,34831,34891,34951,35011,35070,35130,35190,35249,35308,35367,35426,35485,35900,35935,36370,36425,36488,36543,36601,36659,36720,36783,36840,36891,36941,37002,37059,37125,37159,37194,37888,39907,39974,40046,40115,40184,40258,40330,47324,138240,138357,138558,138668,138869,150691,150763,172492,175002,201610,203341,204341,205023", "endLines": "302,430,431,448,449,490,491,595,596,597,598,599,600,601,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,694,695,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,732,761,762,763,764,765,766,767,837,2207,2211,2212,2216,2217,2369,2370,3039,3095,3784,3805,3847,3853", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "13220,18570,18658,19932,19998,22570,22633,29701,29769,29841,29911,29972,30046,30119,32046,32107,32169,32233,32295,32356,32424,32524,32584,32650,32723,32792,32849,32901,32963,33488,33564,33629,33688,33747,33807,33867,33927,33987,34047,34107,34167,34227,34287,34347,34406,34466,34526,34586,34646,34706,34766,34826,34886,34946,35006,35065,35125,35185,35244,35303,35362,35421,35480,35539,35930,35965,36420,36483,36538,36596,36654,36715,36778,36835,36886,36936,36997,37054,37120,37154,37189,37224,37953,39969,40041,40110,40179,40253,40325,40413,47390,138352,138553,138663,138864,138993,150758,150825,172690,175298,203336,204017,205018,205185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "724", "startColumns": "4", "startOffsets": "37409", "endColumns": "53", "endOffsets": "37458"}}, {"source": "E:\\Fiverr_Projects\\PLR_Vault\\my_flutter_app\\build\\google_mobile_ads\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,146,189,232,276,322,364,426,490,534,593,655,710,768,826,880,930,1008,1068,1158,1245,1289,1331,1390,1437,1514,1565,1616", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "97,141,184,227,271,317,359,421,485,529,588,650,705,763,821,875,925,1003,1063,1153,1240,1284,1326,1385,1432,1509,1560,1611,1734"}, "to": {"startLines": "466,467,468,469,470,471,472,473,474,475,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,4676", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21220,21267,21311,21354,21397,21441,21487,21529,21591,21655,30297,30356,30418,30473,30531,30589,30643,30693,30771,30831,30921,31008,31052,31094,31153,31200,31277,31328,235992", "endLines": "466,467,468,469,470,471,472,473,474,475,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,4678", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "21262,21306,21349,21392,21436,21482,21524,21586,21650,21694,30351,30413,30468,30526,30584,30638,30688,30766,30826,30916,31003,31047,31089,31148,31195,31272,31323,31374,236110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e60d7cc8f585e105683d15c0883739b4\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "701,723", "startColumns": "4,4", "startOffsets": "36217,37349", "endColumns": "41,59", "endOffsets": "36254,37404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0f448994e490bf59f20aa19226509e50\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2503,2519,2525,4587,4603", "startColumns": "4,4,4,4,4", "startOffsets": "155403,155828,156006,232308,232719", "endLines": "2518,2524,2534,4602,4606", "endColumns": "24,24,24,24,24", "endOffsets": "155823,156001,156285,232714,232841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "692,702,726,3806,3811", "startColumns": "4,4,4,4,4", "startOffsets": "35809,36259,37513,204022,204192", "endLines": "692,702,726,3810,3814", "endColumns": "56,64,63,24,24", "endOffsets": "35861,36319,37572,204187,204336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "725", "startColumns": "4", "startOffsets": "37463", "endColumns": "49", "endOffsets": "37508"}}]}]}