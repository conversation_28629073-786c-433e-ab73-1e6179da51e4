1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.plrvault.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Permissions for file downloads and storage -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission
12-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:5-5:38
13        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
13-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:4:22-78
14        android:maxSdkVersion="32" />
14-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:5:9-35
15    <uses-permission
15-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:5-7:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:6:22-77
17        android:maxSdkVersion="32" />
17-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:7:9-35
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:5-82
18-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:8:22-79
19
20    <!-- Android 13+ Media permissions -->
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:5-76
21-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:11:22-73
22    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
22-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:5-75
22-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:12:22-72
23    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
23-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:5-75
23-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:13:22-72
24    <!--
25         Required to query activities that can process text, see:
26         https://developer.android.com/training/package-visibility and
27         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
28
29         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
30    -->
31    <queries>
31-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:57:5-62:15
32        <intent>
32-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:58:9-61:18
33            <action android:name="android.intent.action.PROCESS_TEXT" />
33-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:59:13-72
33-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:59:21-70
34
35            <data android:mimeType="text/plain" />
35-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
35-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:19-48
36        </intent>
37        <intent>
37-->[:file_picker] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-16:18
38            <action android:name="android.intent.action.GET_CONTENT" />
38-->[:file_picker] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-72
38-->[:file_picker] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:21-69
39
40            <data android:mimeType="*/*" />
40-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
40-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:19-48
41        </intent> <!-- For browser content -->
42        <intent>
42-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:38:9-44:18
43            <action android:name="android.intent.action.VIEW" />
43-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
43-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
44
45            <category android:name="android.intent.category.BROWSABLE" />
45-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
45-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
46
47            <data android:scheme="https" />
47-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
48        </intent> <!-- End of browser content -->
49        <!-- For CustomTabsService -->
50        <intent>
50-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
51            <action android:name="android.support.customtabs.action.CustomTabsService" />
51-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
51-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
52        </intent> <!-- End of CustomTabsService -->
53        <!-- For MRAID capabilities -->
54        <intent>
54-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
55            <action android:name="android.intent.action.INSERT" />
55-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
55-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
56
57            <data android:mimeType="vnd.android.cursor.dir/event" />
57-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
57-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:19-48
58        </intent>
59        <intent>
59-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
60-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
61
62            <data android:scheme="sms" />
62-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
63        </intent>
64        <intent>
64-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
65            <action android:name="android.intent.action.DIAL" />
65-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
65-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
66
67            <data android:path="tel:" />
67-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
68        </intent>
69    </queries>
70
71    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
71-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
71-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
72    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:22-76
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
74    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
74-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
74-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
75    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
75-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
75-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
76    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
76-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
76-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
77    <uses-permission android:name="android.permission.WAKE_LOCK" />
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:22-65
78    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
78-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
78-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
79
80    <permission
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
81        android:name="com.plrvault.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
81-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
82        android:protectionLevel="signature" />
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
83
84    <uses-permission android:name="com.plrvault.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
84-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
84-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
85
86    <application
87        android:name="android.app.Application"
87-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:17:9-42
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
89        android:extractNativeLibs="true"
90        android:icon="@mipmap/ic_launcher"
90-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:18:9-43
91        android:label="PLR Vault" >
91-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:16:9-34
92        <activity
92-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:19:9-40:20
93            android:name="com.plrvault.app.MainActivity"
93-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:20:13-41
94            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
94-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:25:13-163
95            android:exported="true"
95-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:21:13-36
96            android:hardwareAccelerated="true"
96-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:26:13-47
97            android:launchMode="singleTop"
97-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:22:13-43
98            android:taskAffinity=""
98-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:23:13-36
99            android:theme="@style/LaunchTheme"
99-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:24:13-47
100            android:windowSoftInputMode="adjustResize" >
100-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:27:13-55
101
102            <!--
103                 Specifies an Android theme to apply to this Activity as soon as
104                 the Android process has started. This theme is visible to the user
105                 while the Flutter UI initializes. After that, this theme continues
106                 to determine the Window background behind the Flutter UI.
107            -->
108            <meta-data
108-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:32:13-35:17
109                android:name="io.flutter.embedding.android.NormalTheme"
109-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:33:15-70
110                android:resource="@style/NormalTheme" />
110-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:34:15-52
111
112            <intent-filter>
112-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:36:13-39:29
113                <action android:name="android.intent.action.MAIN" />
113-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:37:17-68
113-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:37:25-66
114
115                <category android:name="android.intent.category.LAUNCHER" />
115-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:38:17-76
115-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:38:27-74
116            </intent-filter>
117        </activity>
118        <!--
119             Don't delete the meta-data below.
120             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
121        -->
122        <meta-data
122-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:43:9-45:33
123            android:name="flutterEmbedding"
123-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:44:13-44
124            android:value="2" />
124-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:45:13-30
125
126        <!-- AdMob App ID -->
127        <meta-data
127-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:48:9-50:69
128            android:name="com.google.android.gms.ads.APPLICATION_ID"
128-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:49:13-69
129            android:value="ca-app-pub-1844424846950062~2197454702" />
129-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:50:13-67
130        <meta-data
130-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:9-12:36
131            android:name="io.flutter.embedded_views_preview"
131-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-61
132            android:value="true" />
132-->[:google_mobile_ads] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\google_mobile_ads\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-33
133
134        <provider
134-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:9-25:20
135            android:name="com.crazecoder.openfile.FileProvider"
135-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-64
136            android:authorities="com.plrvault.app.fileProvider.com.crazecoder.openfile"
136-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-88
137            android:exported="false"
137-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-37
138            android:grantUriPermissions="true" >
138-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-47
139            <meta-data
139-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-24:53
140                android:name="android.support.FILE_PROVIDER_PATHS"
140-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:17-67
141                android:resource="@xml/filepaths" />
141-->[:open_filex] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\open_filex\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:17-50
142        </provider>
143
144        <activity
144-->[:url_launcher_android] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
145            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
145-->[:url_launcher_android] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
146            android:exported="false"
146-->[:url_launcher_android] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
147            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
147-->[:url_launcher_android] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
148
149        <service
149-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:19
150            android:name="com.google.firebase.components.ComponentDiscoveryService"
150-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:18-89
151            android:directBootAware="true"
151-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
152            android:exported="false" >
152-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
153            <meta-data
153-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
154                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
154-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[:firebase_auth] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
156            <meta-data
156-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
157                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
157-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-127
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[:firebase_database] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_database\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
159            <meta-data
159-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
160                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
160-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-126
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[:firebase_storage] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
162            <meta-data
162-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
163                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
163-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[:firebase_core] E:\Fiverr_Projects\PLR_Vault\my_flutter_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
165            <meta-data
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
166                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
166-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
168            <meta-data
168-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:30:13-32:85
169                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
169-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:31:17-118
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:32:17-82
171            <meta-data
171-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:33:13-35:85
172                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
172-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:34:17-107
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-storage:20.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\30fa90dc99de86872ffc10fb84cb808e\transformed\jetified-firebase-storage-20.3.0\AndroidManifest.xml:35:17-82
174            <meta-data
174-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:29:13-31:85
175                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
175-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:30:17-120
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:31:17-82
177            <meta-data
177-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:32:13-34:85
178                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
178-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:33:17-109
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-database:20.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\215e330ea9a96477f126d65d94d59ed3\transformed\jetified-firebase-database-20.3.1\AndroidManifest.xml:34:17-82
180            <meta-data
180-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:25:13-27:85
181                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
181-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:26:17-120
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:27:17-82
183            <meta-data
183-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:28:13-30:85
184                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
184-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:29:17-117
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-appcheck:17.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\521b4fd5c3a61e478483e077dd34d8cf\transformed\jetified-firebase-appcheck-17.1.2\AndroidManifest.xml:30:17-82
186            <meta-data
186-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
187                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
187-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
189            <meta-data
189-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
190                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
190-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
192        </service>
193
194        <activity
194-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
195            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
195-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
196            android:excludeFromRecents="true"
196-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
197            android:exported="true"
197-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
198            android:launchMode="singleTask"
198-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
199            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
199-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
200            <intent-filter>
200-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
201                <action android:name="android.intent.action.VIEW" />
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
202
203                <category android:name="android.intent.category.DEFAULT" />
203-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
203-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
204                <category android:name="android.intent.category.BROWSABLE" />
204-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
204-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
205
206                <data
206-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
207                    android:host="firebase.auth"
208                    android:path="/"
209                    android:scheme="genericidp" />
210            </intent-filter>
211        </activity>
212        <activity
212-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
213            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
213-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
214            android:excludeFromRecents="true"
214-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
215            android:exported="true"
215-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
216            android:launchMode="singleTask"
216-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
217-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
218            <intent-filter>
218-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
219                <action android:name="android.intent.action.VIEW" />
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
220
221                <category android:name="android.intent.category.DEFAULT" />
221-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
221-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
222                <category android:name="android.intent.category.BROWSABLE" />
222-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
222-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
223
224                <data
224-->E:\Fiverr_Projects\PLR_Vault\my_flutter_app\android\app\src\main\AndroidManifest.xml:60:13-50
225                    android:host="firebase.auth"
226                    android:path="/"
227                    android:scheme="recaptcha" />
228            </intent-filter>
229        </activity>
230
231        <uses-library
231-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
232            android:name="androidx.window.extensions"
232-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
233            android:required="false" />
233-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
234        <uses-library
234-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
235            android:name="androidx.window.sidecar"
235-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
236            android:required="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
236-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
237        <activity
237-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
238            android:name="com.google.android.gms.ads.AdActivity"
238-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
239            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
239-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
240            android:exported="false"
240-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
241            android:theme="@android:style/Theme.Translucent" />
241-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
242
243        <provider
243-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
244            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
244-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
245            android:authorities="com.plrvault.app.mobileadsinitprovider"
245-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
246            android:exported="false"
246-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
247            android:initOrder="100" />
247-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
248
249        <service
249-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
250            android:name="com.google.android.gms.ads.AdService"
250-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
251            android:enabled="true"
251-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
252            android:exported="false" />
252-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
253
254        <activity
254-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
255            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
255-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
256            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
256-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
257            android:exported="false" />
257-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
258        <activity
258-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
259            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
259-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
260            android:excludeFromRecents="true"
260-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
261            android:exported="false"
261-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
262            android:launchMode="singleTask"
262-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
263            android:taskAffinity=""
263-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
264            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
264-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
265
266        <property
266-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
267            android:name="android.adservices.AD_SERVICES_CONFIG"
267-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:108:13-65
268            android:resource="@xml/gma_ad_services_config" />
268-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
269
270        <activity
270-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
271            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
271-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
272            android:excludeFromRecents="true"
272-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
273            android:exported="false"
273-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
274            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
274-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
275        <!--
276            Service handling Google Sign-In user revocation. For apps that do not integrate with
277            Google Sign-In, this service will never be started.
278        -->
279        <service
279-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
280            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
280-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
281            android:exported="true"
281-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
282            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
282-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
283            android:visibleToInstantApps="true" />
283-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
284
285        <provider
285-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
286            android:name="com.google.firebase.provider.FirebaseInitProvider"
286-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
287            android:authorities="com.plrvault.app.firebaseinitprovider"
287-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
288            android:directBootAware="true"
288-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
289            android:exported="false"
289-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
290            android:initOrder="100" />
290-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
291
292        <activity
292-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
293            android:name="com.google.android.gms.common.api.GoogleApiActivity"
293-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
294            android:exported="false"
294-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
295            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
295-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7976d4e64729cb9c47971e21b0850b04\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
296
297        <provider
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
298            android:name="androidx.startup.InitializationProvider"
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
299            android:authorities="com.plrvault.app.androidx-startup"
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
300            android:exported="false" >
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
301            <meta-data
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
302                android:name="androidx.work.WorkManagerInitializer"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
303                android:value="androidx.startup" />
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
304            <meta-data
304-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
305                android:name="androidx.emoji2.text.EmojiCompatInitializer"
305-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
306                android:value="androidx.startup" />
306-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2f9e30b92e09a4c79f34f925067bc407\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
307            <meta-data
307-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
308                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
308-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
309                android:value="androidx.startup" />
309-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
310            <meta-data
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
311                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
312                android:value="androidx.startup" />
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
313        </provider>
314
315        <service
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
316            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
317            android:directBootAware="false"
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
318            android:enabled="@bool/enable_system_alarm_service_default"
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
319            android:exported="false" />
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
320        <service
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
321            android:name="androidx.work.impl.background.systemjob.SystemJobService"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
323            android:enabled="@bool/enable_system_job_service_default"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
324            android:exported="true"
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
325            android:permission="android.permission.BIND_JOB_SERVICE" />
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
326        <service
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
327            android:name="androidx.work.impl.foreground.SystemForegroundService"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
329            android:enabled="@bool/enable_system_foreground_service_default"
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
330            android:exported="false" />
330-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
331
332        <receiver
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
333            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
335            android:enabled="true"
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
337        <receiver
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
338            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
340            android:enabled="false"
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
341            android:exported="false" >
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
342            <intent-filter>
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
343                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
344                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
344-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
345            </intent-filter>
346        </receiver>
347        <receiver
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
348            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
349            android:directBootAware="false"
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
350            android:enabled="false"
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
351            android:exported="false" >
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
352            <intent-filter>
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
353                <action android:name="android.intent.action.BATTERY_OKAY" />
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
354                <action android:name="android.intent.action.BATTERY_LOW" />
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
354-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
355            </intent-filter>
356        </receiver>
357        <receiver
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
358            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
359            android:directBootAware="false"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
360            android:enabled="false"
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
361            android:exported="false" >
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
362            <intent-filter>
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
363                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
364                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
364-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
365            </intent-filter>
366        </receiver>
367        <receiver
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
368            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
369            android:directBootAware="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
370            android:enabled="false"
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
371            android:exported="false" >
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
372            <intent-filter>
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
373                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
373-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
374            </intent-filter>
375        </receiver>
376        <receiver
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
377            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
378            android:directBootAware="false"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
379            android:enabled="false"
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
380            android:exported="false" >
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
381            <intent-filter>
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
382                <action android:name="android.intent.action.BOOT_COMPLETED" />
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
383                <action android:name="android.intent.action.TIME_SET" />
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
384                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
385            </intent-filter>
386        </receiver>
387        <receiver
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
388            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
390            android:enabled="@bool/enable_system_alarm_service_default"
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
391            android:exported="false" >
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
392            <intent-filter>
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
393                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
394            </intent-filter>
395        </receiver>
396        <receiver
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
397            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
398            android:directBootAware="false"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
399            android:enabled="true"
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
400            android:exported="true"
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
401            android:permission="android.permission.DUMP" >
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
402            <intent-filter>
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
403                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
404            </intent-filter>
405        </receiver>
406
407        <uses-library
407-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
408            android:name="android.ext.adservices"
408-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
409            android:required="false" />
409-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
410
411        <meta-data
411-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
412            android:name="com.google.android.gms.version"
412-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
413            android:value="@integer/google_play_services_version" />
413-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
414
415        <receiver
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
416            android:name="androidx.profileinstaller.ProfileInstallReceiver"
416-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
417            android:directBootAware="false"
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
418            android:enabled="true"
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
419            android:exported="true"
419-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
420            android:permission="android.permission.DUMP" >
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
422                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
425                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
426            </intent-filter>
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
428                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
431                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
431-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
431-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
432            </intent-filter>
433        </receiver>
434
435        <service
435-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
436            android:name="androidx.room.MultiInstanceInvalidationService"
436-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
437            android:directBootAware="true"
437-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
438            android:exported="false" />
438-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
439    </application>
440
441</manifest>
