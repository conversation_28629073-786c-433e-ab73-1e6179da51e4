import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../utils/logger.dart';

enum AdminRole { superAdmin, admin }

class AdminUser {
  final String email;
  final AdminRole role;
  final DateTime addedAt;
  final String addedBy;

  AdminUser({
    required this.email,
    required this.role,
    required this.addedAt,
    required this.addedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'role': role.name,
      'addedAt': addedAt.millisecondsSinceEpoch,
      'addedBy': addedBy,
    };
  }

  factory AdminUser.fromMap(Map<dynamic, dynamic> map) {
    return AdminUser(
      email: map['email'] ?? '',
      role: AdminRole.values.firstWhere(
        (r) => r.name == map['role'],
        orElse: () => AdminRole.admin,
      ),
      addedAt: DateTime.fromMillisecondsSinceEpoch(map['addedAt'] ?? 0),
      addedBy: map['addedBy'] ?? '',
    );
  }
}

class AdminAuthService {
  static const String _superAdminEmail = '<EMAIL>';
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final DatabaseReference _adminsRef = FirebaseDatabase.instance
      .ref()
      .child('admins');

  static bool _isLoggedIn = false;
  static AdminUser? _currentAdmin;

  static bool get isLoggedIn => _isLoggedIn;
  static AdminUser? get currentAdmin => _currentAdmin;
  static String? get adminEmail => _currentAdmin?.email;
  static bool get isSuperAdmin => _currentAdmin?.role == AdminRole.superAdmin;

  /// Initialize super admin in database if not exists
  static Future<void> initializeSuperAdmin() async {
    try {
      final emailKey = _superAdminEmail
          .replaceAll('.', '_')
          .replaceAll('@', '_');
      final snapshot = await _adminsRef.child(emailKey).once();
      if (!snapshot.snapshot.exists) {
        final superAdmin = AdminUser(
          email: _superAdminEmail,
          role: AdminRole.superAdmin,
          addedAt: DateTime.now(),
          addedBy: 'system',
        );
        await _adminsRef.child(emailKey).set(superAdmin.toMap());
        Logger.info('Super admin initialized: $_superAdminEmail');
      }
    } catch (e) {
      Logger.error('Failed to initialize super admin', e);
    }
  }

  /// Check if current user is an admin and log them in
  static Future<bool> checkAndLoginAdmin() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final adminUser = await getAdminByEmail(user.email!);
      if (adminUser != null) {
        _isLoggedIn = true;
        _currentAdmin = adminUser;
        Logger.info('Admin logged in: ${user.email}');
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Failed to check admin status', e);
      return false;
    }
  }

  /// Set current admin (used by main app flow)
  static void setCurrentAdmin(AdminUser adminUser) {
    _isLoggedIn = true;
    _currentAdmin = adminUser;
    Logger.info(
      'Admin set: ${adminUser.email} with role: ${adminUser.role.name}',
    );
  }

  /// Get admin user by UID (always fresh from database)
  static Future<AdminUser?> getAdminByUID(String uid) async {
    try {
      Logger.info('Fresh admin check for UID: $uid');

      // Create completely fresh database reference using UID
      final DatabaseReference freshRef = FirebaseDatabase.instance.ref(
        'admins/$uid',
      );
      final DatabaseEvent event = await freshRef.once();

      if (event.snapshot.exists && event.snapshot.value != null) {
        final data = event.snapshot.value as Map<dynamic, dynamic>;
        final adminUser = AdminUser.fromMap(data);
        Logger.info(
          '✅ Admin found: ${adminUser.email} (UID: ${adminUser.uid}) - Role: ${adminUser.role.name}',
        );
        return adminUser;
      } else {
        Logger.info('❌ No admin record found for UID: $uid');
        return null;
      }
    } catch (e) {
      Logger.error('❌ Database error for UID $uid: $e');
      return null;
    }
  }

  /// Get admin user by email (for backward compatibility)
  static Future<AdminUser?> getAdminByEmail(String email) async {
    // This method is now deprecated but kept for compatibility
    // We'll search through all admins to find by email
    try {
      final snapshot = await _adminsRef.once();
      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (final entry in data.entries) {
          final adminData = entry.value as Map<dynamic, dynamic>;
          if (adminData['email'] == email) {
            return AdminUser.fromMap(adminData);
          }
        }
      }
      return null;
    } catch (e) {
      Logger.error('❌ Error searching admin by email: $e');
      return null;
    }
  }

  /// Force refresh admin status (completely fresh check)
  static Future<AdminUser?> forceRefreshAdminStatus(String email) async {
    try {
      Logger.info('🔄 Force refreshing admin status for: $email');

      // Complete session reset
      _isLoggedIn = false;
      _currentAdmin = null;

      // Small delay to ensure state is cleared
      await Future.delayed(const Duration(milliseconds: 100));

      // Fresh database query
      final adminUser = await getAdminByEmail(email);

      if (adminUser != null) {
        Logger.info(
          '✅ Force refresh successful - Admin: ${adminUser.role.name}',
        );
      } else {
        Logger.info('✅ Force refresh successful - Regular user');
      }

      return adminUser;
    } catch (e) {
      Logger.error('❌ Force refresh failed: $e');
      return null;
    }
  }

  /// Get all admins (Super admin only)
  static Future<List<AdminUser>> getAllAdmins() async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can view all admins');
      }

      final snapshot = await _adminsRef.once();
      final List<AdminUser> admins = [];

      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (final entry in data.entries) {
          admins.add(AdminUser.fromMap(entry.value));
        }
      }

      return admins;
    } catch (e) {
      Logger.error('Failed to get all admins', e);
      throw Exception('Failed to get admins: $e');
    }
  }

  /// Add new admin by UID (Super admin only)
  static Future<void> addAdminByUID(
    String uid,
    String email,
    AdminRole role,
  ) async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can add new admins');
      }

      if (uid == _superAdminUID) {
        throw Exception('Cannot modify super admin');
      }

      final existingAdmin = await getAdminByUID(uid);
      if (existingAdmin != null) {
        throw Exception('User is already an admin');
      }

      final newAdmin = AdminUser(
        uid: uid,
        email: email,
        role: role,
        addedAt: DateTime.now(),
        addedBy: _currentAdmin!.email,
      );

      await _adminsRef.child(uid).set(newAdmin.toMap());
      Logger.info(
        'New admin added: $email (UID: $uid) with role: ${role.name}',
      );
    } catch (e) {
      Logger.error('Failed to add admin', e);
      throw Exception('Failed to add admin: $e');
    }
  }

  /// Add new admin (Super admin only) - Deprecated, use addAdminByUID
  static Future<void> addAdmin(String email, AdminRole role) async {
    throw Exception('This method is deprecated. Use addAdminByUID instead.');
  }

  /// Remove admin by UID (Super admin only)
  static Future<void> removeAdminByUID(String uid) async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can remove admins');
      }

      if (uid == _superAdminUID) {
        throw Exception('Cannot remove super admin');
      }

      await _adminsRef.child(uid).remove();
      Logger.info('Admin removed: UID $uid');
    } catch (e) {
      Logger.error('Failed to remove admin', e);
      throw Exception('Failed to remove admin: $e');
    }
  }

  /// Remove admin (Super admin only) - Deprecated, use removeAdminByUID
  static Future<void> removeAdmin(String email) async {
    throw Exception('This method is deprecated. Use removeAdminByUID instead.');
  }

  /// Legacy authentication method (kept for backward compatibility)
  static bool authenticate(String email, String password) {
    // This method is deprecated but kept for existing code compatibility
    return false;
  }

  /// Complete logout - clears all admin session data
  static void logout() {
    _isLoggedIn = false;
    _currentAdmin = null;
    Logger.info('🧹 Admin session cleared via logout()');
  }

  /// Clear admin session completely (used on app restart or fresh login)
  static void clearSession() {
    _isLoggedIn = false;
    _currentAdmin = null;
    Logger.info('🧹 Admin session completely cleared via clearSession()');
  }

  /// Force complete logout with Firebase Auth
  static Future<void> forceCompleteLogout() async {
    try {
      Logger.info('🚪 Starting force complete logout');

      // Clear local admin session
      _isLoggedIn = false;
      _currentAdmin = null;

      // Sign out from Firebase Auth
      await _auth.signOut();

      Logger.info('✅ Force complete logout finished');
    } catch (e) {
      Logger.error('❌ Error in force complete logout: $e');
      throw Exception('Failed to logout completely: $e');
    }
  }
}
