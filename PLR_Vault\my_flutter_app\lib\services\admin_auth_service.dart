import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../utils/logger.dart';

enum AdminRole { superAdmin, admin }

class AdminUser {
  final String email;
  final AdminRole role;
  final DateTime addedAt;
  final String addedBy;

  AdminUser({
    required this.email,
    required this.role,
    required this.addedAt,
    required this.addedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'email': email,
      'role': role.name,
      'addedAt': addedAt.millisecondsSinceEpoch,
      'addedBy': addedBy,
    };
  }

  factory AdminUser.fromMap(Map<dynamic, dynamic> map) {
    return AdminUser(
      email: map['email'] ?? '',
      role: AdminRole.values.firstWhere(
        (r) => r.name == map['role'],
        orElse: () => AdminRole.admin,
      ),
      addedAt: DateTime.fromMillisecondsSinceEpoch(map['addedAt'] ?? 0),
      addedBy: map['addedBy'] ?? '',
    );
  }
}

class AdminAuthService {
  static const String _superAdminEmail = '<EMAIL>';
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final DatabaseReference _adminsRef = FirebaseDatabase.instance
      .ref()
      .child('admins');

  static bool _isLoggedIn = false;
  static AdminUser? _currentAdmin;

  static bool get isLoggedIn => _isLoggedIn;
  static AdminUser? get currentAdmin => _currentAdmin;
  static String? get adminEmail => _currentAdmin?.email;
  static bool get isSuperAdmin => _currentAdmin?.role == AdminRole.superAdmin;

  /// Initialize super admin in database if not exists
  static Future<void> initializeSuperAdmin() async {
    try {
      final snapshot = await _adminsRef
          .child(_superAdminEmail.replaceAll('.', '_'))
          .once();
      if (!snapshot.snapshot.exists) {
        final superAdmin = AdminUser(
          email: _superAdminEmail,
          role: AdminRole.superAdmin,
          addedAt: DateTime.now(),
          addedBy: 'system',
        );
        await _adminsRef
            .child(_superAdminEmail.replaceAll('.', '_'))
            .set(superAdmin.toMap());
        Logger.info('Super admin initialized: $_superAdminEmail');
      }
    } catch (e) {
      Logger.error('Failed to initialize super admin', e);
    }
  }

  /// Check if current user is an admin and log them in
  static Future<bool> checkAndLoginAdmin() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final adminUser = await getAdminByEmail(user.email!);
      if (adminUser != null) {
        _isLoggedIn = true;
        _currentAdmin = adminUser;
        Logger.info('Admin logged in: ${user.email}');
        return true;
      }
      return false;
    } catch (e) {
      Logger.error('Failed to check admin status', e);
      return false;
    }
  }

  /// Set current admin (used by main app flow)
  static void setCurrentAdmin(AdminUser adminUser) {
    _isLoggedIn = true;
    _currentAdmin = adminUser;
    Logger.info(
      'Admin set: ${adminUser.email} with role: ${adminUser.role.name}',
    );
  }

  /// Get admin user by email (always fresh from database)
  static Future<AdminUser?> getAdminByEmail(String email) async {
    try {
      final emailKey = email.replaceAll('.', '_');
      Logger.info('Checking admin status for email: $email (key: $emailKey)');

      // Always get fresh data from database, no caching
      final snapshot = await _adminsRef.child(emailKey).once();

      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        final adminUser = AdminUser.fromMap(data);
        Logger.info(
          'Admin found: ${adminUser.email} with role: ${adminUser.role.name}',
        );
        return adminUser;
      } else {
        Logger.info('No admin found for email: $email');
        return null;
      }
    } catch (e) {
      Logger.error('Failed to get admin by email: $email', e);
      return null;
    }
  }

  /// Get all admins (Super admin only)
  static Future<List<AdminUser>> getAllAdmins() async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can view all admins');
      }

      final snapshot = await _adminsRef.once();
      final List<AdminUser> admins = [];

      if (snapshot.snapshot.exists) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (final entry in data.entries) {
          admins.add(AdminUser.fromMap(entry.value));
        }
      }

      return admins;
    } catch (e) {
      Logger.error('Failed to get all admins', e);
      throw Exception('Failed to get admins: $e');
    }
  }

  /// Add new admin (Super admin only)
  static Future<void> addAdmin(String email, AdminRole role) async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can add new admins');
      }

      if (email == _superAdminEmail) {
        throw Exception('Cannot modify super admin');
      }

      final existingAdmin = await getAdminByEmail(email);
      if (existingAdmin != null) {
        throw Exception('User is already an admin');
      }

      final newAdmin = AdminUser(
        email: email,
        role: role,
        addedAt: DateTime.now(),
        addedBy: _currentAdmin!.email,
      );

      await _adminsRef.child(email.replaceAll('.', '_')).set(newAdmin.toMap());
      Logger.info('New admin added: $email with role: ${role.name}');
    } catch (e) {
      Logger.error('Failed to add admin', e);
      throw Exception('Failed to add admin: $e');
    }
  }

  /// Remove admin (Super admin only)
  static Future<void> removeAdmin(String email) async {
    try {
      if (!isSuperAdmin) {
        throw Exception('Only super admin can remove admins');
      }

      if (email == _superAdminEmail) {
        throw Exception('Cannot remove super admin');
      }

      await _adminsRef.child(email.replaceAll('.', '_')).remove();
      Logger.info('Admin removed: $email');
    } catch (e) {
      Logger.error('Failed to remove admin', e);
      throw Exception('Failed to remove admin: $e');
    }
  }

  /// Legacy authentication method (kept for backward compatibility)
  static bool authenticate(String email, String password) {
    // This method is deprecated but kept for existing code compatibility
    return false;
  }

  static void logout() {
    _isLoggedIn = false;
    _currentAdmin = null;
    Logger.info('Admin session cleared');
  }

  /// Clear admin session completely (used on app restart or fresh login)
  static void clearSession() {
    _isLoggedIn = false;
    _currentAdmin = null;
    Logger.info('Admin session completely cleared');
  }
}
