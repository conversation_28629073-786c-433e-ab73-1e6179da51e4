class AdminAuthService {
  static const String _adminEmail = '<EMAIL>';
  static const String _adminPassword = '123456';

  static bool _isLoggedIn = false;

  /// Check if admin is currently logged in
  static bool get isLoggedIn => _isLoggedIn;

  /// Authenticate admin with email and password
  static bool authenticate(String email, String password) {
    final trimmedEmail = email.trim().toLowerCase();

    if (trimmedEmail == _adminEmail && password == _adminPassword) {
      _isLoggedIn = true;
      return true;
    }
    return false;
  }

  /// Logout admin
  static void logout() {
    _isLoggedIn = false;
  }

  /// Get admin email
  static String get adminEmail => _adminEmail;
}
