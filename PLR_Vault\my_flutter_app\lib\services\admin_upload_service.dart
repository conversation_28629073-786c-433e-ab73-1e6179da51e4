import 'dart:io';
import 'package:path/path.dart' as path;
import 'firebase_storage_service.dart';
import 'realtime_database_service.dart';
import '../models/content_item.dart';

class AdminUploadService {
  final FirebaseStorageService _storageService = FirebaseStorageService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  /// Get all available categories
  List<String> getAvailableCategories() {
    return [
      'Business & Entrepreneurship',
      'Health & Wellness',
      'Technology & Innovation',
      'Recipes & Nutrition',
      'MRR Video Courses',
      'Finance & Investment',
      'Self-Improvement & Motivation',
      'Marketing & Branding',
      'Design & Templates',
      'Spirituality & Mindfulness',
      'Career & Freelancing',
      'AI & Automation',
      'Education & eLearning',
      'Legal & Business Docs',
      'eCommerce & Dropshipping',
      'Parenting & Family',
      'Fashion & Beauty',
      'Travel & Lifestyle',
      'Kids & Learning',
      'Entertainment & Fun',
    ];
  }

  /// Upload a single file to Firebase
  Future<String> uploadFile({
    required File file,
    required String category,
    required String title,
    required String description,
    Function(double)? onProgress,
  }) async {
    try {
      final String fileName = path.basename(file.path);

      // Upload to Firebase Storage
      final String downloadUrl = await _storageService.uploadFile(
        file: file,
        category: category,
        fileName: fileName,
        onProgress: onProgress,
      );

      // Create content item for database
      final ContentItem contentItem = ContentItem(
        id: _generateId(fileName, category),
        title: title.isNotEmpty ? title : _generateTitle(fileName),
        description: description.isNotEmpty
            ? description
            : _generateDescription(fileName, category),
        category: category,
        fileUrl: downloadUrl,
        fileName: fileName,
        fileType: path.extension(fileName).substring(1),
        fileSize: await file.length(),
        createdAt: DateTime.now(),
      );

      // Add to Realtime Database
      await _dbService.addContentItem(contentItem);

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  /// Generate a unique ID for content
  String _generateId(String fileName, String category) {
    final String cleanFileName = fileName
        .replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_')
        .toLowerCase();
    final String cleanCategory = category
        .replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_')
        .toLowerCase();
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return '${cleanCategory}_${cleanFileName}_$timestamp';
  }

  /// Generate title from filename
  String _generateTitle(String fileName) {
    final String nameWithoutExtension = path.basenameWithoutExtension(fileName);
    final String title = nameWithoutExtension
        .replaceAll(RegExp(r'[_-]'), ' ')
        .split(' ')
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : '',
        )
        .join(' ');
    return title;
  }

  /// Generate description based on file and category
  String _generateDescription(String fileName, String category) {
    final String fileType = path.extension(fileName).substring(1).toUpperCase();
    return 'A $fileType resource in the $category category. This content provides valuable information and insights for your learning and development.';
  }
}
