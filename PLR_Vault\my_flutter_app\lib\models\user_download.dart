class UserDownload {
  final String contentId;
  final String title;
  final String category;
  final String fileUrl;
  final String fileName;
  final String localPath;
  final DateTime downloadedAt;
  final int fileSize;
  final bool isDownloaded;

  UserDownload({
    required this.contentId,
    required this.title,
    required this.category,
    required this.fileUrl,
    required this.fileName,
    required this.localPath,
    required this.downloadedAt,
    required this.fileSize,
    this.isDownloaded = true,
  });

  // Convert from Firebase Realtime Database Map
  factory UserDownload.fromMap(String contentId, Map<dynamic, dynamic> map) {
    return UserDownload(
      contentId: contentId,
      title: map['title'] ?? '',
      category: map['category'] ?? '',
      fileUrl: map['fileUrl'] ?? '',
      fileName: map['fileName'] ?? '',
      localPath: map['localPath'] ?? '',
      downloadedAt: DateTime.parse(map['downloadedAt'] ?? DateTime.now().toIso8601String()),
      fileSize: map['fileSize'] ?? 0,
      isDownloaded: map['isDownloaded'] ?? true,
    );
  }

  // Convert to Firebase Realtime Database Map
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'category': category,
      'fileUrl': fileUrl,
      'fileName': fileName,
      'localPath': localPath,
      'downloadedAt': downloadedAt.toIso8601String(),
      'fileSize': fileSize,
      'isDownloaded': isDownloaded,
    };
  }

  // Get file extension from fileName
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  // Check if file is a PDF
  bool get isPdf => fileExtension == 'pdf';

  // Check if file is a video
  bool get isVideo => ['mp4', 'mov', 'avi', 'mkv'].contains(fileExtension);

  // Check if file is a text file
  bool get isText => ['txt', 'doc', 'docx'].contains(fileExtension);

  // Get appropriate icon for file type
  String get fileIcon {
    if (isPdf) return '📄';
    if (isVideo) return '🎥';
    if (isText) return '📝';
    return '📁';
  }

  // Format file size for display
  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  // Format download date for display
  String get formattedDownloadDate {
    final now = DateTime.now();
    final difference = now.difference(downloadedAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'UserDownload(contentId: $contentId, title: $title, category: $category, isDownloaded: $isDownloaded)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserDownload && other.contentId == contentId;
  }

  @override
  int get hashCode => contentId.hashCode;
}
