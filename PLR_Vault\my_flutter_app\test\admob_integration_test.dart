import 'package:flutter_test/flutter_test.dart';
import 'package:plr_vault/models/content_item.dart';

void main() {
  group('Content Item Tests', () {
    test('ContentItem should correctly identify video files', () {
      final videoContent = ContentItem(
        id: 'test_video',
        title: 'Test Video',
        description: 'A test video file',
        category: 'Test',
        fileUrl: 'https://example.com/video.mp4',
        fileName: 'video.mp4',
        fileType: 'mp4',
        fileSize: 1024000,
        createdAt: DateTime.now(),
      );

      expect(videoContent.isVideo, true);
      expect(videoContent.isPdf, false);
      expect(videoContent.isText, false);
    });

    test('ContentItem should correctly identify PDF files', () {
      final pdfContent = ContentItem(
        id: 'test_pdf',
        title: 'Test PDF',
        description: 'A test PDF file',
        category: 'Test',
        fileUrl: 'https://example.com/document.pdf',
        fileName: 'document.pdf',
        fileType: 'pdf',
        fileSize: 512000,
        createdAt: DateTime.now(),
      );

      expect(pdfContent.isVideo, false);
      expect(pdfContent.isPdf, true);
      expect(pdfContent.isText, false);
    });

    test('ContentItem should correctly identify text files', () {
      final textContent = ContentItem(
        id: 'test_txt',
        title: 'Test Text',
        description: 'A test text file',
        category: 'Test',
        fileUrl: 'https://example.com/document.txt',
        fileName: 'document.txt',
        fileType: 'txt',
        fileSize: 1024,
        createdAt: DateTime.now(),
      );

      expect(textContent.isVideo, false);
      expect(textContent.isPdf, false);
      expect(textContent.isText, true);
    });

    test('ContentItem should format file sizes correctly', () {
      final smallFile = ContentItem(
        id: 'small',
        title: 'Small File',
        description: 'A small file',
        category: 'Test',
        fileUrl: 'https://example.com/small.txt',
        fileName: 'small.txt',
        fileType: 'txt',
        fileSize: 512,
        createdAt: DateTime.now(),
      );

      final mediumFile = ContentItem(
        id: 'medium',
        title: 'Medium File',
        description: 'A medium file',
        category: 'Test',
        fileUrl: 'https://example.com/medium.pdf',
        fileName: 'medium.pdf',
        fileType: 'pdf',
        fileSize: 1024 * 1024, // 1MB
        createdAt: DateTime.now(),
      );

      expect(smallFile.formattedFileSize, '512B');
      expect(mediumFile.formattedFileSize, '1.0MB');
    });
  });
}
